import { Router, Response } from 'express';
import { body, param } from 'express-validator';
import { validate } from '../middleware/validation';
import { asyncHandler } from '../utils/async-handler';
import { authMiddleware, AuthenticatedRequest } from '../middleware/auth';
import { prisma } from '@piknowkyo/database/client';
import { createError } from '../middleware/error-handler';
import { ERROR_CODES } from '@piknowkyo/shared/constants';
import { AITaskType } from '@piknowkyo/shared/types';

const router = Router();

// Validation rules
const createTaskConfigValidation = [
  body('taskType')
    .isIn([
      'research', 'content_analysis', 'script_generation', 'translation',
      'image_generation', 'image_analysis', 'metadata_generation', 'voice_script',
      'video_planning', 'transition_planning', 'animation_planning', 'seo_optimization',
      'hashtag_generation', 'thumbnail_text', 'fallback'
    ])
    .withMessage('Invalid task type'),
  body('providerId')
    .isUUID()
    .withMessage('Invalid provider ID'),
  body('modelName')
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('Model name must be between 1 and 200 characters'),
  body('temperature')
    .isFloat({ min: 0, max: 2 })
    .withMessage('Temperature must be between 0 and 2'),
  body('maxTokens')
    .isInt({ min: 100, max: 32000 })
    .withMessage('Max tokens must be between 100 and 32000'),
  body('topP')
    .optional()
    .isFloat({ min: 0, max: 1 })
    .withMessage('Top P must be between 0 and 1'),
  body('frequencyPenalty')
    .optional()
    .isFloat({ min: -2, max: 2 })
    .withMessage('Frequency penalty must be between -2 and 2'),
  body('presencePenalty')
    .optional()
    .isFloat({ min: -2, max: 2 })
    .withMessage('Presence penalty must be between -2 and 2'),
  body('customPrompt')
    .optional()
    .isLength({ max: 5000 })
    .withMessage('Custom prompt must be less than 5000 characters'),
  body('fallbackProviderId')
    .optional()
    .isUUID()
    .withMessage('Invalid fallback provider ID'),
  body('fallbackModelName')
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage('Fallback model name must be less than 200 characters'),
  body('priority')
    .optional()
    .isInt({ min: 1, max: 10 })
    .withMessage('Priority must be between 1 and 10'),
  body('maxRetries')
    .optional()
    .isInt({ min: 1, max: 10 })
    .withMessage('Max retries must be between 1 and 10'),
  body('retryDelay')
    .optional()
    .isInt({ min: 1, max: 300 })
    .withMessage('Retry delay must be between 1 and 300 seconds')
];

// GET /api/settings/ai-tasks - Get all AI task configurations for user
router.get('/', authMiddleware, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;

  // Ensure user settings exist
  let userSettings = await prisma.userSettings.findUnique({
    where: { userId }
  });

  if (!userSettings) {
    userSettings = await prisma.userSettings.create({
      data: { userId }
    });
  }

  // Get all AI task configurations
  const configurations = await prisma.userAISettings.findMany({
    where: { userSettingsId: userSettings.id },
    include: {
      aiProvider: {
        include: {
          models: true,
          apiKeys: {
            select: {
              id: true,
              keyName: true,
              isActive: true,
              lastUsed: true,
              usageCount: true
            }
          }
        }
      }
    },
    orderBy: { taskType: 'asc' }
  });

  // Transform to match frontend interface
  const transformedConfigs = configurations.map(config => ({
    id: config.id,
    taskType: config.taskType as AITaskType,
    providerId: config.aiProviderId,
    modelName: config.modelName,
    temperature: config.temperature,
    maxTokens: config.maxTokens,
    topP: config.topP || undefined,
    frequencyPenalty: config.frequencyPenalty || undefined,
    presencePenalty: config.presencePenalty || undefined,
    customPrompt: config.customPrompt || undefined,
    fallbackProviderId: config.fallbackProviderId || undefined,
    fallbackModelName: config.fallbackModelName || undefined,
    isActive: config.isActive,
    priority: config.priority || 1,
    maxRetries: config.maxRetries || 3,
    retryDelay: config.retryDelay || 5,
    requiresInternetAccess: config.requiresInternetAccess || false,
    provider: config.aiProvider
  }));

  res.json({
    success: true,
    data: transformedConfigs,
    timestamp: new Date().toISOString()
  });
}));

// POST /api/settings/ai-tasks - Create or update AI task configuration
router.post('/', authMiddleware, validate(createTaskConfigValidation), asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;
  const {
    taskType,
    providerId,
    modelName,
    temperature,
    maxTokens,
    topP,
    frequencyPenalty,
    presencePenalty,
    customPrompt,
    fallbackProviderId,
    fallbackModelName,
    priority,
    maxRetries,
    retryDelay,
    isActive = true
  } = req.body;

  // Verify AI provider exists
  const aiProvider = await prisma.aIProvider.findUnique({
    where: { id: providerId }
  });

  if (!aiProvider) {
    throw createError('AI provider not found', 404, ERROR_CODES.NOT_FOUND);
  }

  // Verify model exists for provider
  const model = await prisma.aIModel.findFirst({
    where: {
      providerId,
      name: modelName,
      isActive: true
    }
  });

  if (!model) {
    throw createError('Model not found for this provider', 404, ERROR_CODES.NOT_FOUND);
  }

  // Verify fallback provider if specified
  if (fallbackProviderId) {
    const fallbackProvider = await prisma.aIProvider.findUnique({
      where: { id: fallbackProviderId }
    });

    if (!fallbackProvider) {
      throw createError('Fallback provider not found', 404, ERROR_CODES.NOT_FOUND);
    }

    if (fallbackModelName) {
      const fallbackModel = await prisma.aIModel.findFirst({
        where: {
          providerId: fallbackProviderId,
          name: fallbackModelName,
          isActive: true
        }
      });

      if (!fallbackModel) {
        throw createError('Fallback model not found for fallback provider', 404, ERROR_CODES.NOT_FOUND);
      }
    }
  }

  // Ensure user settings exist
  let userSettings = await prisma.userSettings.findUnique({
    where: { userId }
  });

  if (!userSettings) {
    userSettings = await prisma.userSettings.create({
      data: { userId }
    });
  }

  // Check if task requires internet access
  const requiresInternetAccess = ['research', 'seo_optimization'].includes(taskType);

  // Create or update configuration
  const configuration = await prisma.userAISettings.upsert({
    where: {
      userSettingsId_taskType: {
        userSettingsId: userSettings.id,
        taskType
      }
    },
    create: {
      userSettingsId: userSettings.id,
      taskType,
      aiProviderId: providerId,
      modelName,
      temperature,
      maxTokens,
      topP,
      frequencyPenalty,
      presencePenalty,
      customPrompt,
      fallbackProviderId,
      fallbackModelName,
      priority: priority || 1,
      maxRetries: maxRetries || 3,
      retryDelay: retryDelay || 5,
      requiresInternetAccess,
      isActive
    },
    update: {
      aiProviderId: providerId,
      modelName,
      temperature,
      maxTokens,
      topP,
      frequencyPenalty,
      presencePenalty,
      customPrompt,
      fallbackProviderId,
      fallbackModelName,
      priority: priority || 1,
      maxRetries: maxRetries || 3,
      retryDelay: retryDelay || 5,
      requiresInternetAccess,
      isActive,
      updatedAt: new Date()
    },
    include: {
      aiProvider: true
    }
  });

  res.json({
    success: true,
    data: {
      id: configuration.id,
      taskType: configuration.taskType as AITaskType,
      providerId: configuration.aiProviderId,
      modelName: configuration.modelName,
      temperature: configuration.temperature,
      maxTokens: configuration.maxTokens,
      topP: configuration.topP || undefined,
      frequencyPenalty: configuration.frequencyPenalty || undefined,
      presencePenalty: configuration.presencePenalty || undefined,
      customPrompt: configuration.customPrompt || undefined,
      fallbackProviderId: configuration.fallbackProviderId || undefined,
      fallbackModelName: configuration.fallbackModelName || undefined,
      isActive: configuration.isActive,
      priority: configuration.priority || 1,
      maxRetries: configuration.maxRetries || 3,
      retryDelay: configuration.retryDelay || 5,
      requiresInternetAccess: configuration.requiresInternetAccess || false,
      provider: configuration.aiProvider
    },
    message: 'AI task configuration saved successfully',
    timestamp: new Date().toISOString()
  });
}));

// DELETE /api/settings/ai-tasks/:taskType - Delete AI task configuration
router.delete('/:taskType', authMiddleware, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;
  const { taskType } = req.params;

  const userSettings = await prisma.userSettings.findUnique({
    where: { userId }
  });

  if (!userSettings) {
    throw createError('User settings not found', 404, ERROR_CODES.NOT_FOUND);
  }

  const deleted = await prisma.userAISettings.deleteMany({
    where: {
      userSettingsId: userSettings.id,
      taskType
    }
  });

  if (deleted.count === 0) {
    throw createError('Configuration not found', 404, ERROR_CODES.NOT_FOUND);
  }

  res.json({
    success: true,
    message: 'AI task configuration deleted successfully',
    timestamp: new Date().toISOString()
  });
}));

// GET /api/settings/ai-tasks/task-types - Get available task types with descriptions
router.get('/task-types', authMiddleware, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const taskTypes = [
    {
      value: 'research',
      label: 'Recherche Internet',
      description: 'Recherche de tendances et d\'informations sur internet',
      requiresInternetAccess: true
    },
    {
      value: 'content_analysis',
      label: 'Analyse de Contenu',
      description: 'Analyse et compréhension du contenu existant',
      requiresInternetAccess: false
    },
    {
      value: 'script_generation',
      label: 'Génération de Scripts',
      description: 'Création de scripts pour les vidéos',
      requiresInternetAccess: false
    },
    {
      value: 'translation',
      label: 'Traduction',
      description: 'Traduction multilingue du contenu',
      requiresInternetAccess: false
    },
    {
      value: 'image_generation',
      label: 'Génération d\'Images',
      description: 'Création d\'images et de miniatures',
      requiresInternetAccess: false
    },
    {
      value: 'image_analysis',
      label: 'Analyse d\'Images',
      description: 'Analyse et description d\'images',
      requiresInternetAccess: false
    },
    {
      value: 'metadata_generation',
      label: 'Métadonnées',
      description: 'Génération de titres, descriptions et tags',
      requiresInternetAccess: false
    },
    {
      value: 'voice_script',
      label: 'Script Vocal',
      description: 'Optimisation des scripts pour la voix',
      requiresInternetAccess: false
    },
    {
      value: 'video_planning',
      label: 'Planification Vidéo',
      description: 'Planification de la structure vidéo',
      requiresInternetAccess: false
    },
    {
      value: 'transition_planning',
      label: 'Transitions',
      description: 'Planification des transitions vidéo',
      requiresInternetAccess: false
    },
    {
      value: 'animation_planning',
      label: 'Animations',
      description: 'Planification des séquences d\'animation',
      requiresInternetAccess: false
    },
    {
      value: 'seo_optimization',
      label: 'Optimisation SEO',
      description: 'Optimisation du contenu pour le SEO',
      requiresInternetAccess: true
    },
    {
      value: 'hashtag_generation',
      label: 'Hashtags',
      description: 'Génération de hashtags pour les réseaux sociaux',
      requiresInternetAccess: false
    },
    {
      value: 'thumbnail_text',
      label: 'Texte Miniature',
      description: 'Génération de texte pour les miniatures',
      requiresInternetAccess: false
    },
    {
      value: 'fallback',
      label: 'Fallback',
      description: 'Configuration de secours en cas d\'échec',
      requiresInternetAccess: false
    }
  ];

  res.json({
    success: true,
    data: taskTypes,
    timestamp: new Date().toISOString()
  });
}));

export default router;
