'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { apiHelpers } from '@/lib/api';

interface VideoProjectForm {
  title: string;
  description: string;
  primaryLanguage: string;
  targetLanguages: string[];
  content: string;
  settings: {
    duration: number;
    style: string;
  };
}

export default function NewVideoProjectPage() {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isGeneratingAuto, setIsGeneratingAuto] = useState(false);
  const [form, setForm] = useState<VideoProjectForm>({
    title: '',
    description: '',
    primaryLanguage: 'ENGLISH',
    targetLanguages: [],
    content: '',
    settings: {
      duration: 60,
      style: 'professional'
    }
  });

  const languages = [
    { value: 'ENGLISH', label: 'English' },
    { value: 'FRENCH', label: 'French' },
    { value: 'SPANISH', label: 'Spanish' }
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await apiHelpers.post('/videos', form);

      if (response.data.success) {
        router.push(`/videos/${response.data.data.id}`);
      } else {
        alert(`Error: ${response.data.message || 'Failed to create video project'}`);
      }
    } catch (error: any) {
      console.error('Error creating video project:', error);
      alert(error.response?.data?.message || 'Failed to create video project. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleLanguageToggle = (language: string) => {
    setForm(prev => ({
      ...prev,
      targetLanguages: prev.targetLanguages.includes(language)
        ? prev.targetLanguages.filter(l => l !== language)
        : [...prev.targetLanguages, language]
    }));
  };

  const handleAutoGenerate = async () => {
    setIsGeneratingAuto(true);

    try {
      const response = await apiHelpers.post('/videos/generate-auto', {
        language: form.primaryLanguage,
        duration: form.settings.duration,
        style: form.settings.style
      });

      if (response.data.success) {
        // Redirect to the generated video
        router.push(`/videos/${response.data.data.videoId}`);
      } else {
        throw new Error(response.data.message || 'Failed to generate video');
      }
    } catch (error: any) {
      console.error('Auto generation failed:', error);
      alert(error.response?.data?.message || 'Failed to generate video automatically. Please try again.');
    } finally {
      setIsGeneratingAuto(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!user) {
    router.push('/auth/login');
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container-wide py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Create New Video Project</h1>
          <p className="mt-2 text-gray-600">
            Set up your multilingual video content project
          </p>
        </div>

        <div className="max-w-2xl">
          {/* Auto Generation Option */}
          <div className="card mb-6">
            <div className="card-body">
              <h2 className="text-xl font-semibold text-gray-900 mb-3">🤖 Génération Automatique</h2>
              <p className="text-gray-600 mb-4">
                Laissez l'IA créer automatiquement une vidéo complète pour vous.
                Aucune configuration manuelle requise !
              </p>
              <button
                type="button"
                onClick={handleAutoGenerate}
                disabled={isGeneratingAuto}
                className="btn-primary w-full"
              >
                {isGeneratingAuto ? (
                  <>
                    <LoadingSpinner size="sm" />
                    Génération en cours...
                  </>
                ) : (
                  '🎬 Créer une vidéo automatiquement'
                )}
              </button>
            </div>
          </div>

          {/* Manual Creation Form */}
          <div className="card">
            <div className="card-body">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">📝 Création Manuelle</h2>
              <p className="text-gray-600 mb-6">
                Ou créez votre projet vidéo manuellement avec vos propres paramètres.
              </p>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="card mt-4">
            <div className="card-body space-y-6">
              <div>
                <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                  Project Title
                </label>
                <input
                  type="text"
                  id="title"
                  required
                  className="input-field"
                  value={form.title}
                  onChange={(e) => setForm(prev => ({ ...prev, title: e.target.value }))}
                  placeholder="Enter your video project title"
                />
              </div>

              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  id="description"
                  rows={3}
                  className="input-field"
                  value={form.description}
                  onChange={(e) => setForm(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Describe your video project"
                />
              </div>

              <div>
                <label htmlFor="primaryLanguage" className="block text-sm font-medium text-gray-700 mb-2">
                  Primary Language
                </label>
                <select
                  id="primaryLanguage"
                  className="input-field"
                  value={form.primaryLanguage}
                  onChange={(e) => setForm(prev => ({ ...prev, primaryLanguage: e.target.value }))}
                >
                  {languages.map(lang => (
                    <option key={lang.value} value={lang.value}>
                      {lang.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Target Languages for Translation
                </label>
                <div className="space-y-2">
                  {languages.filter(lang => lang.value !== form.primaryLanguage).map(lang => (
                    <label key={lang.value} className="flex items-center">
                      <input
                        type="checkbox"
                        className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                        checked={form.targetLanguages.includes(lang.value)}
                        onChange={() => handleLanguageToggle(lang.value)}
                      />
                      <span className="ml-2 text-sm text-gray-700">{lang.label}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-2">
                  Content/Script
                </label>
                <textarea
                  id="content"
                  rows={6}
                  required
                  className="input-field"
                  value={form.content}
                  onChange={(e) => setForm(prev => ({ ...prev, content: e.target.value }))}
                  placeholder="Enter your video script or content outline"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label htmlFor="duration" className="block text-sm font-medium text-gray-700 mb-2">
                    Duration (seconds)
                  </label>
                  <input
                    type="number"
                    id="duration"
                    min="30"
                    max="300"
                    className="input-field"
                    value={form.settings.duration}
                    onChange={(e) => setForm(prev => ({
                      ...prev,
                      settings: { ...prev.settings, duration: parseInt(e.target.value) }
                    }))}
                  />
                </div>

                <div>
                  <label htmlFor="style" className="block text-sm font-medium text-gray-700 mb-2">
                    Video Style
                  </label>
                  <select
                    id="style"
                    className="input-field"
                    value={form.settings.style}
                    onChange={(e) => setForm(prev => ({
                      ...prev,
                      settings: { ...prev.settings, style: e.target.value }
                    }))}
                  >
                    <option value="professional">Professional</option>
                    <option value="casual">Casual</option>
                    <option value="educational">Educational</option>
                    <option value="entertainment">Entertainment</option>
                  </select>
                </div>
              </div>

              <div className="flex gap-4 pt-4">
                <button
                  type="button"
                  onClick={() => router.back()}
                  className="btn-outline flex-1"
                  disabled={isSubmitting}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="btn-primary flex-1"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <LoadingSpinner size="sm" />
                      Creating...
                    </>
                  ) : (
                    'Create Project'
                  )}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
