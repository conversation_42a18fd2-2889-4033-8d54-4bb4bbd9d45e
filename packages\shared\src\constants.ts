// Error codes used throughout the application
export const ERROR_CODES = {
  // General errors
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  ALREADY_EXISTS: 'ALREADY_EXISTS',
  FORBIDDEN: 'FORBIDDEN',
  
  // Authentication errors
  UNAUTHORIZED: 'UNAUTHORIZED',
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  
  // API errors
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  API_KEY_INVALID: 'API_KEY_INVALID',
  API_QUOTA_EXCEEDED: 'API_QUOTA_EXCEEDED',
  REQUEST_TOO_LARGE: 'REQUEST_TOO_LARGE',
  MISSING_REQUIRED_FIELD: 'MISSING_REQUIRED_FIELD',
  
  // File errors
  FILE_TOO_LARGE: 'FILE_TOO_LARGE',
  INVALID_FILE_TYPE: 'INVALID_FILE_TYPE',
  FILE_UPLOAD_ERROR: 'FILE_UPLOAD_ERROR',
  
  // Video processing errors
  VIDEO_PROCESSING_ERROR: 'VIDEO_PROCESSING_ERROR',
  AUDIO_PROCESSING_ERROR: 'AUDIO_PROCESSING_ERROR',
  
  // AI Provider errors
  AI_PROVIDER_ERROR: 'AI_PROVIDER_ERROR',
  MODEL_NOT_AVAILABLE: 'MODEL_NOT_AVAILABLE',
  
  // TTS errors
  TTS_PROVIDER_ERROR: 'TTS_PROVIDER_ERROR',
  VOICE_NOT_AVAILABLE: 'VOICE_NOT_AVAILABLE'
} as const;

// AI Provider constants
export const AI_PROVIDERS = {
  OPENAI: 'OPENAI',
  ANTHROPIC: 'ANTHROPIC',
  GOOGLE: 'GOOGLE',
  MISTRAL: 'MISTRAL',
  COHERE: 'COHERE'
} as const;

// TTS Provider constants
export const TTS_PROVIDERS = {
  OPENAI: 'OPENAI',
  ELEVENLABS: 'ELEVENLABS',
  GOOGLE: 'GOOGLE',
  AZURE: 'AZURE',
  AWS: 'AWS'
} as const;

// Language constants
export const LANGUAGES = {
  ENGLISH: 'ENGLISH',
  FRENCH: 'FRENCH',
  SPANISH: 'SPANISH',
  GERMAN: 'GERMAN',
  ITALIAN: 'ITALIAN',
  PORTUGUESE: 'PORTUGUESE',
  DUTCH: 'DUTCH',
  RUSSIAN: 'RUSSIAN',
  CHINESE: 'CHINESE',
  JAPANESE: 'JAPANESE',
  KOREAN: 'KOREAN',
  ARABIC: 'ARABIC'
} as const;

// Video project status constants
export const VIDEO_PROJECT_STATUS = {
  DRAFT: 'DRAFT',
  PROCESSING: 'PROCESSING',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
  CANCELLED: 'CANCELLED'
} as const;

// Voice gender constants
export const VOICE_GENDER = {
  MALE: 'MALE',
  FEMALE: 'FEMALE',
  NEUTRAL: 'NEUTRAL'
} as const;

// User roles
export const USER_ROLES = {
  USER: 'USER',
  ADMIN: 'ADMIN',
  SUPER_ADMIN: 'SUPER_ADMIN'
} as const;

// API limits
export const API_LIMITS = {
  MAX_FILE_SIZE: 100 * 1024 * 1024, // 100MB
  MAX_VIDEO_DURATION: 600, // 10 minutes
  MAX_REQUESTS_PER_MINUTE: 60,
  MAX_REQUESTS_PER_HOUR: 1000,
  MAX_REQUESTS_PER_DAY: 10000
} as const;

// File types
export const ALLOWED_FILE_TYPES = {
  IMAGES: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
  VIDEOS: ['video/mp4', 'video/webm', 'video/quicktime'],
  AUDIO: ['audio/mpeg', 'audio/wav', 'audio/ogg', 'audio/mp3']
} as const;
