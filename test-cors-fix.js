const axios = require('axios');

async function testCORSFix() {
  try {
    console.log('🧪 Testing CORS Fix...\n');
    
    // Test 1: Direct backend call
    console.log('1. Testing direct backend API call...');
    const email = `cors-test-${Date.now()}@example.com`;
    
    const directResponse = await axios.post('http://localhost:5000/api/auth/register', {
      email: email,
      password: 'Test123!',
      name: 'CORS Test User'
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:3000'  // Simulate frontend origin
      },
      timeout: 10000
    });
    
    console.log('✅ Direct backend call successful!');
    console.log('Status:', directResponse.status);
    console.log('CORS headers present:', !!directResponse.headers['access-control-allow-origin']);
    
    // Test 2: Preflight request simulation
    console.log('\n2. Testing CORS preflight request...');
    try {
      const preflightResponse = await axios.options('http://localhost:5000/api/auth/register', {
        headers: {
          'Origin': 'http://localhost:3000',
          'Access-Control-Request-Method': 'POST',
          'Access-Control-Request-Headers': 'Content-Type, Authorization'
        },
        timeout: 5000
      });
      
      console.log('✅ Preflight request successful!');
      console.log('Status:', preflightResponse.status);
      console.log('Access-Control-Allow-Origin:', preflightResponse.headers['access-control-allow-origin']);
      console.log('Access-Control-Allow-Methods:', preflightResponse.headers['access-control-allow-methods']);
      
    } catch (preflightError) {
      console.log('⚠️ Preflight request info:', preflightError.response?.status || 'No response');
      // This might be expected depending on CORS implementation
    }
    
    // Test 3: Frontend proxy test
    console.log('\n3. Testing frontend proxy...');
    const frontendEmail = `frontend-cors-${Date.now()}@example.com`;
    
    const frontendResponse = await axios.post('http://localhost:3000/api/auth/register', {
      email: frontendEmail,
      password: 'Test123!',
      name: 'Frontend CORS Test'
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });
    
    console.log('✅ Frontend proxy successful!');
    console.log('Status:', frontendResponse.status);
    console.log('User created via frontend:', frontendResponse.data.data.user.email);
    
    console.log('\n🎉 CORS Fix Verification Complete!');
    console.log('✅ Backend CORS configured for: http://localhost:3000');
    console.log('✅ Frontend can communicate with backend');
    console.log('✅ Registration endpoint working');
    
    console.log('\n📝 Summary:');
    console.log('- Backend running on: http://localhost:5000');
    console.log('- Frontend running on: http://localhost:3000');
    console.log('- CORS properly configured');
    console.log('- Authentication endpoints working');
    
    console.log('\n🚀 You can now use the application!');
    console.log('Open http://localhost:3000 and try registering a user.');
    
  } catch (error) {
    console.error('❌ CORS test failed:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Headers:', error.response.headers);
      console.error('Data:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error('Error:', error.message);
    }
    
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Make sure backend is running on port 5000');
    console.log('2. Make sure frontend is running on port 3000');
    console.log('3. Check that FRONTEND_URL in .env is set to http://localhost:3000');
    console.log('4. Restart both backend and frontend after changes');
  }
}

testCORSFix();
