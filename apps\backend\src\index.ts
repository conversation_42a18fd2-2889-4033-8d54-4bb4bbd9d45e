import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';
import { createServer } from 'http';
import path from 'path';

import { logger } from './config/logger';
import { errorHandler } from './middleware/error-handler';
import { authMiddleware } from './middleware/auth';
import { validateRequest } from './middleware/validation';
import { AutoSetupService } from './services/initialization/auto-setup.service';
import {
  apiRateLimit,
  speedLimiter,
  sanitizeInput,
  securityHeaders,
  requestSizeLimit,
  securityLogger
} from './middleware/security';

// Import routes
import authRoutes from './routes/auth';
import userRoutes from './routes/user';
import videoRoutes from './routes/video';
import aiProviderRoutes from './routes/ai-provider';
import aiTaskConfigRoutes from './routes/ai-task-config';
import videoGenerationConfigRoutes from './routes/video-generation-config';
import voiceRoutes from './routes/voice';
import settingsRoutes from './routes/settings';

import healthRoutes from './routes/health';

// Load environment variables from root directory
dotenv.config({ path: path.resolve(__dirname, '../../../.env') });

const app = express();
const server = createServer(app);

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false
}));

// CORS configuration
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Security middleware
app.use(securityLogger);
app.use(securityHeaders);
app.use(sanitizeInput);
app.use(requestSizeLimit('10mb'));

// Rate limiting and speed limiting
app.use('/api/', apiRateLimit);
app.use('/api/', speedLimiter);

// Body parsing middleware
app.use(compression());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging middleware
app.use(morgan('combined', {
  stream: {
    write: (message: string) => logger.info(message.trim())
  }
}));

// Health check endpoint (before auth)
app.use('/health', healthRoutes);

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/users', authMiddleware, userRoutes);
app.use('/api/videos', authMiddleware, videoRoutes);
app.use('/api/ai-providers', authMiddleware, aiProviderRoutes);
app.use('/api/settings/ai-tasks', authMiddleware, aiTaskConfigRoutes);
app.use('/api/settings/video-generation', authMiddleware, videoGenerationConfigRoutes);
app.use('/api/voices', authMiddleware, voiceRoutes);
app.use('/api/settings', authMiddleware, settingsRoutes);


// Static file serving for uploads
app.use('/uploads', express.static('uploads'));

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Route not found',
    message: `Cannot ${req.method} ${req.originalUrl}`,
    timestamp: new Date().toISOString()
  });
});

// Global error handler
app.use(errorHandler);

// Graceful shutdown
const gracefulShutdown = (signal: string) => {
  logger.info(`Received ${signal}. Starting graceful shutdown...`);
  
  server.close(() => {
    logger.info('HTTP server closed.');
    process.exit(0);
  });

  // Force close after 30 seconds
  setTimeout(() => {
    logger.error('Could not close connections in time, forcefully shutting down');
    process.exit(1);
  }, 30000);
};

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Start server
// Force PORT to 5001 for backend to avoid conflicts with frontend
const PORT = 5001;
const HOST = process.env.HOST || '0.0.0.0';

server.listen(PORT, HOST, async () => {
  logger.info(`🚀 Server running on http://${HOST}:${PORT}`);
  logger.info(`📚 API Documentation: http://${HOST}:${PORT}/api/docs`);
  logger.info(`🏥 Health Check: http://${HOST}:${PORT}/health`);
  logger.info(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);

  // Initialize providers automatically
  try {
    const autoSetup = new AutoSetupService();
    await autoSetup.initializeDefaultProviders();
  } catch (error) {
    logger.error('Failed to initialize default providers:', error);
  }
});

export default app;
