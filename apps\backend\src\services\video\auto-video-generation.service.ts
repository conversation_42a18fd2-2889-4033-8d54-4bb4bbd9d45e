import { prisma } from '@piknowkyo/database/client';
import { logger } from '../../config/logger';
import { AIProviderService } from '../ai/ai-provider.service';
import { VoiceService } from '../voice/voice.service';
import { createError } from '../../middleware/error-handler';
import { ERROR_CODES } from '@piknowkyo/shared/constants';



interface AutoVideoRequest {
  topic?: string;
  language?: string;
  duration?: number;
  style?: string;
}

interface VideoGenerationResult {
  videoId: string;
  title: string;
  description: string;
  script: string;
  status: string;
  estimatedDuration: number;
}

export class AutoVideoGenerationService {
  private aiProviderService: AIProviderService;
  private voiceService: VoiceService;

  constructor() {
    this.aiProviderService = new AIProviderService();
    this.voiceService = new VoiceService();
  }

  async generateVideoAutomatically(
    userId: string, 
    request: AutoVideoRequest = {}
  ): Promise<VideoGenerationResult> {
    logger.info(`🎬 Starting automatic video generation for user ${userId}`);

    try {
      // Step 1: Generate topic if not provided
      const topic = request.topic || await this.generateRandomTopic();
      logger.info(`📝 Topic: ${topic}`);

      // Step 2: Generate video title
      const title = await this.generateTitle(topic, request.language);
      logger.info(`🏷️ Title: ${title}`);

      // Step 3: Generate video script
      const script = await this.generateScript(topic, title, request.language, request.duration);
      logger.info(`📜 Script generated (${script.length} characters)`);

      // Step 4: Generate description
      const description = await this.generateDescription(title, script, request.language);
      logger.info(`📄 Description generated`);

      // Step 5: Create video project in database
      const video = await prisma.videoProject.create({
        data: {
          userId,
          title,
          description,
          primaryLanguage: (request.language || 'ENGLISH') as any,
          metadata: JSON.stringify({
            topic,
            script,
            autoGenerated: true,
            generatedAt: new Date().toISOString(),
            requestedDuration: request.duration || 60,
            style: request.style || 'informative'
          }),
          status: 'SCRIPT_GENERATED'
        }
      });

      // Step 6: Start background processing for images and audio
      this.processVideoInBackground(video.id, script, request).catch(error => {
        logger.error(`Background processing failed for video ${video.id}:`, error);
      });

      return {
        videoId: video.id,
        title,
        description,
        script,
        status: 'SCRIPT_GENERATED',
        estimatedDuration: request.duration || 60
      };

    } catch (error) {
      logger.error('Failed to generate video automatically:', error);
      throw createError(
        'Failed to generate video automatically',
        500,
        ERROR_CODES.VIDEO_GENERATION_ERROR
      );
    }
  }

  private async generateRandomTopic(): Promise<string> {
    const topics = [
      "The fascinating world of artificial intelligence",
      "Climate change and sustainable solutions",
      "The future of space exploration",
      "Healthy lifestyle tips for busy people",
      "The history and evolution of technology",
      "Amazing facts about the ocean",
      "The science behind renewable energy",
      "How to boost productivity and focus",
      "The art of effective communication",
      "Discovering hidden gems in travel destinations"
    ];

    return topics[Math.floor(Math.random() * topics.length)];
  }

  private async generateTitle(topic: string, language?: string): Promise<string> {
    const prompt = `Create a catchy, engaging YouTube video title about "${topic}". 
    The title should be:
    - Attention-grabbing and clickable
    - SEO-friendly
    - Under 60 characters
    - In ${language || 'English'} language
    
    Return only the title, nothing else.`;

    return await this.callAI(prompt, 'title_generation');
  }

  private async generateScript(topic: string, title: string, language?: string, duration?: number): Promise<string> {
    const targetWords = Math.floor((duration || 60) * 2.5); // ~150 words per minute speaking rate
    
    const prompt = `Write a compelling video script about "${topic}" with the title "${title}".
    
    Requirements:
    - Target length: approximately ${targetWords} words
    - Language: ${language || 'English'}
    - Engaging introduction that hooks viewers
    - Clear, informative content with interesting facts
    - Strong conclusion with call-to-action
    - Natural speaking rhythm
    - Include brief pauses marked with [PAUSE]
    
    Format the script as natural speech, ready for text-to-speech conversion.
    Return only the script content, no additional formatting or comments.`;

    return await this.callAI(prompt, 'script_writing');
  }

  private async generateDescription(title: string, script: string, language?: string): Promise<string> {
    const prompt = `Create a YouTube video description for a video titled "${title}".
    
    Based on this script content: "${script.substring(0, 500)}..."
    
    The description should:
    - Be engaging and informative
    - Include relevant keywords for SEO
    - Be 2-3 paragraphs long
    - Language: ${language || 'English'}
    - Include a call-to-action
    
    Return only the description, nothing else.`;

    return await this.callAI(prompt, 'description_generation');
  }

  private async callAI(prompt: string, taskType: string): Promise<string> {
    // Get AI configuration for this task type
    const aiConfig = await this.getAIConfigForTask(taskType);
    
    if (!aiConfig) {
      throw createError(`No AI configuration found for task: ${taskType}`, 500, ERROR_CODES.AI_PROVIDER_ERROR);
    }

    // Make AI request
    const response = await this.aiProviderService.sendRequest(aiConfig.aiProviderId, {
      model: aiConfig.modelName,
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: aiConfig.temperature,
      maxTokens: aiConfig.maxTokens
    });

    return response.choices[0]?.message?.content || '';
  }

  private async getAIConfigForTask(taskType: string) {
    // First try to get user-specific configuration, then fall back to system default
    const config = await prisma.userAISettings.findFirst({
      where: {
        taskType,
        isActive: true
      },
      include: {
        userSettings: true
      },
      orderBy: [
        { userSettings: { userId: 'asc' } }, // System user first
        { createdAt: 'desc' }
      ]
    });

    return config;
  }

  private async processVideoInBackground(videoId: string, script: string, request: AutoVideoRequest): Promise<void> {
    try {
      logger.info(`🎨 Starting background processing for video ${videoId}`);

      // Update status
      await prisma.videoProject.update({
        where: { id: videoId },
        data: { status: 'PROCESSING' }
      });

      // Generate images (placeholder for now)
      logger.info(`🖼️ Generating images for video ${videoId}`);
      await this.generateImages(script, request.style);

      // Generate audio (placeholder for now)
      logger.info(`🎵 Generating audio for video ${videoId}`);
      await this.generateAudio(script, request.language);

      // Update status to completed
      await prisma.videoProject.update({
        where: { id: videoId },
        data: { 
          status: 'COMPLETED',
          completedAt: new Date()
        }
      });

      logger.info(`✅ Video ${videoId} processing completed`);

    } catch (error) {
      logger.error(`❌ Background processing failed for video ${videoId}:`, error);
      
      // Update status to failed
      await prisma.videoProject.update({
        where: { id: videoId },
        data: { 
          status: 'FAILED',
          metadata: JSON.stringify({
            error: (error as Error).message,
            failedAt: new Date().toISOString()
          })
        }
      });
    }
  }

  private async generateImages(script: string, style?: string): Promise<void> {
    // Placeholder for image generation
    // This would analyze the script and generate relevant images
    logger.info('Image generation placeholder - would generate images based on script content');
    
    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 2000));
  }

  private async generateAudio(script: string, language?: string): Promise<void> {
    // Placeholder for audio generation
    // This would convert the script to speech using TTS providers
    logger.info('Audio generation placeholder - would convert script to speech');
    
    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 3000));
  }
}
