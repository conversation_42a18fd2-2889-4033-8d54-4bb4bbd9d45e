{
  error: Error: Package subpath './generated' is not defined by "exports" in C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\@piknowkyo\database\package.json
      at exportsNotFound (node:internal/modules/esm/resolve:296:10)
      at packageExportsResolve (node:internal/modules/esm/resolve:643:9)
      at resolveExports (node:internal/modules/cjs/loader:640:36)
      at Function._findPath (node:internal/modules/cjs/loader:748:31)
      at Function.<anonymous> (node:internal/modules/cjs/loader:1235:27)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12) {
    code: 'ERR_PACKAGE_PATH_NOT_EXPORTED'
  },
  level: 'error',
  message: `uncaughtException: Package subpath './generated' is not defined by "exports" in C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\@piknowkyo\\database\\package.json\n` +
    `Error: Package subpath './generated' is not defined by "exports" in C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\@piknowkyo\\database\\package.json\n` +
    '    at exportsNotFound (node:internal/modules/esm/resolve:296:10)\n' +
    '    at packageExportsResolve (node:internal/modules/esm/resolve:643:9)\n' +
    '    at resolveExports (node:internal/modules/cjs/loader:640:36)\n' +
    '    at Function._findPath (node:internal/modules/cjs/loader:748:31)\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1235:27)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)',
  stack: `Error: Package subpath './generated' is not defined by "exports" in C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\@piknowkyo\\database\\package.json\n` +
    '    at exportsNotFound (node:internal/modules/esm/resolve:296:10)\n' +
    '    at packageExportsResolve (node:internal/modules/esm/resolve:643:9)\n' +
    '    at resolveExports (node:internal/modules/cjs/loader:640:36)\n' +
    '    at Function._findPath (node:internal/modules/cjs/loader:748:31)\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1235:27)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)',
  exception: true,
  date: 'Mon Jul 28 2025 07:52:38 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 16632,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 114524160,
      heapTotal: 32235520,
      heapUsed: 17698008,
      external: 5578960,
      arrayBuffers: 2461497
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1671570.906 },
  trace: [
    {
      column: 10,
      file: 'node:internal/modules/esm/resolve',
      function: 'exportsNotFound',
      line: 296,
      method: null,
      native: false
    },
    {
      column: 9,
      file: 'node:internal/modules/esm/resolve',
      function: 'packageExportsResolve',
      line: 643,
      method: null,
      native: false
    },
    {
      column: 36,
      file: 'node:internal/modules/cjs/loader',
      function: 'resolveExports',
      line: 640,
      method: null,
      native: false
    },
    {
      column: 31,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._findPath',
      line: 748,
      method: '_findPath',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1235,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    }
  ],
  timestamp: '2025-07-28 07:52:38'
}
{
  error: Error: Cannot find module 'C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\@piknowkyo\shared\dist\constants\index.js'
      at createEsmNotFoundErr (node:internal/modules/cjs/loader:1286:15)
      at finalizeEsmResolution (node:internal/modules/cjs/loader:1274:15)
      at resolveExports (node:internal/modules/cjs/loader:640:14)
      at Function._findPath (node:internal/modules/cjs/loader:748:31)
      at Function.<anonymous> (node:internal/modules/cjs/loader:1235:27)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12) {
    code: 'MODULE_NOT_FOUND',
    path: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\@piknowkyo\\shared\\package.json'
  },
  level: 'error',
  message: "uncaughtException: Cannot find module 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\@piknowkyo\\shared\\dist\\constants\\index.js'\n" +
    "Error: Cannot find module 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\@piknowkyo\\shared\\dist\\constants\\index.js'\n" +
    '    at createEsmNotFoundErr (node:internal/modules/cjs/loader:1286:15)\n' +
    '    at finalizeEsmResolution (node:internal/modules/cjs/loader:1274:15)\n' +
    '    at resolveExports (node:internal/modules/cjs/loader:640:14)\n' +
    '    at Function._findPath (node:internal/modules/cjs/loader:748:31)\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1235:27)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)',
  stack: "Error: Cannot find module 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\@piknowkyo\\shared\\dist\\constants\\index.js'\n" +
    '    at createEsmNotFoundErr (node:internal/modules/cjs/loader:1286:15)\n' +
    '    at finalizeEsmResolution (node:internal/modules/cjs/loader:1274:15)\n' +
    '    at resolveExports (node:internal/modules/cjs/loader:640:14)\n' +
    '    at Function._findPath (node:internal/modules/cjs/loader:748:31)\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1235:27)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)',
  exception: true,
  date: 'Mon Jul 28 2025 07:53:34 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 35468,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 112902144,
      heapTotal: 56885248,
      heapUsed: 27401568,
      external: 4748762,
      arrayBuffers: 844970
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1671626.671 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: 'createEsmNotFoundErr',
      line: 1286,
      method: null,
      native: false
    },
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: 'finalizeEsmResolution',
      line: 1274,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'resolveExports',
      line: 640,
      method: null,
      native: false
    },
    {
      column: 31,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._findPath',
      line: 748,
      method: '_findPath',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1235,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    }
  ],
  timestamp: '2025-07-28 07:53:34'
}
{
  error: Error: Cannot find module 'C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\@piknowkyo\shared\dist\constants\index.js'
      at createEsmNotFoundErr (node:internal/modules/cjs/loader:1286:15)
      at finalizeEsmResolution (node:internal/modules/cjs/loader:1274:15)
      at resolveExports (node:internal/modules/cjs/loader:640:14)
      at Function._findPath (node:internal/modules/cjs/loader:748:31)
      at Function.<anonymous> (node:internal/modules/cjs/loader:1235:27)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12) {
    code: 'MODULE_NOT_FOUND',
    path: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\@piknowkyo\\shared\\package.json'
  },
  level: 'error',
  message: "uncaughtException: Cannot find module 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\@piknowkyo\\shared\\dist\\constants\\index.js'\n" +
    "Error: Cannot find module 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\@piknowkyo\\shared\\dist\\constants\\index.js'\n" +
    '    at createEsmNotFoundErr (node:internal/modules/cjs/loader:1286:15)\n' +
    '    at finalizeEsmResolution (node:internal/modules/cjs/loader:1274:15)\n' +
    '    at resolveExports (node:internal/modules/cjs/loader:640:14)\n' +
    '    at Function._findPath (node:internal/modules/cjs/loader:748:31)\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1235:27)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)',
  stack: "Error: Cannot find module 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\@piknowkyo\\shared\\dist\\constants\\index.js'\n" +
    '    at createEsmNotFoundErr (node:internal/modules/cjs/loader:1286:15)\n' +
    '    at finalizeEsmResolution (node:internal/modules/cjs/loader:1274:15)\n' +
    '    at resolveExports (node:internal/modules/cjs/loader:640:14)\n' +
    '    at Function._findPath (node:internal/modules/cjs/loader:748:31)\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1235:27)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)',
  exception: true,
  date: 'Mon Jul 28 2025 07:57:38 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 17680,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 86593536,
      heapTotal: 33787904,
      heapUsed: 23830000,
      external: 3170117,
      arrayBuffers: 52741
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1671870.281 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: 'createEsmNotFoundErr',
      line: 1286,
      method: null,
      native: false
    },
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: 'finalizeEsmResolution',
      line: 1274,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'resolveExports',
      line: 640,
      method: null,
      native: false
    },
    {
      column: 31,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._findPath',
      line: 748,
      method: '_findPath',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1235,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    }
  ],
  timestamp: '2025-07-28 07:57:38'
}
{
  error: Error: Cannot find module '@database/client'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts:3:24
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:24\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:24\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 08:15:32 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 15924,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 104398848,
      heapTotal: 35749888,
      heapUsed: 21215312,
      external: 3228681,
      arrayBuffers: 120974
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1672944.5 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 24,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:15:32'
}
{
  error: Error: Cannot find module '@database/client'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts:3:24
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:24\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:24\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 08:15:45 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 20504,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 103890944,
      heapTotal: 35749888,
      heapUsed: 20869768,
      external: 3181546,
      arrayBuffers: 73775
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1672957.89 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 24,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:15:45'
}
{
  error: Error: Cannot find module '@database/client'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts:3:24
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:24\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:24\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 08:16:03 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 25252,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 104005632,
      heapTotal: 35487744,
      heapUsed: 20866904,
      external: 3181586,
      arrayBuffers: 73815
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1672975.312 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 24,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:16:03'
}
{
  error: Error: Cannot find module '@database/client'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts:3:24
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:24\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:24\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 08:16:18 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 4860,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 103903232,
      heapTotal: 35749888,
      heapUsed: 20851016,
      external: 3181604,
      arrayBuffers: 73833
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1672990.265 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 24,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:16:18'
}
{
  error: Error: Cannot find module '@database/client'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts:3:24
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:24\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:24\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 08:16:29 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 24552,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 103944192,
      heapTotal: 35749888,
      heapUsed: 20833544,
      external: 3181564,
      arrayBuffers: 73793
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1673001.796 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 24,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:16:29'
}
{
  error: Error: Cannot find module '@database/client'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts:3:24
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:24\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:24\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 08:16:55 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 34508,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 93491200,
      heapTotal: 35749888,
      heapUsed: 20731680,
      external: 3170117,
      arrayBuffers: 62338
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1673028.062 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 24,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:16:55'
}
{
  error: Error: Cannot find module '@database/client'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts:3:24
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:24\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:24\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 08:17:31 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 19932,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 92942336,
      heapTotal: 35487744,
      heapUsed: 20733312,
      external: 3170117,
      arrayBuffers: 62338
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1673063.578 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 24,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:17:31'
}
{
  error: Error: Cannot find module '@database/client'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts:3:24
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:24\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:24\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 08:17:41 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 29816,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 93011968,
      heapTotal: 35749888,
      heapUsed: 20496496,
      external: 3170117,
      arrayBuffers: 62338
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1673073.796 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 24,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:17:41'
}
{
  error: Error: Cannot find module '@database/client'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts:3:24
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:24\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:24\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 08:17:50 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 14996,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 93470720,
      heapTotal: 36012032,
      heapUsed: 20782800,
      external: 3170117,
      arrayBuffers: 62338
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1673082.812 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 24,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:17:50'
}
{
  error: Error: Cannot find module '@database/client'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts:3:24
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:24\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:24\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 08:18:06 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 9152,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 93835264,
      heapTotal: 35749888,
      heapUsed: 20779800,
      external: 3170117,
      arrayBuffers: 62338
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1673099.187 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 24,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:18:06'
}
{
  error: Error: Cannot find module '@database/client'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts:3:24
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:24\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:24\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 08:18:17 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 28868,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 93405184,
      heapTotal: 35487744,
      heapUsed: 20732440,
      external: 3170117,
      arrayBuffers: 62338
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1673109.546 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 24,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:18:17'
}
{
  error: Error: Cannot find module '@database/client'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts:3:24
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:24\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:24\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 08:18:27 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 31676,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 93761536,
      heapTotal: 35749888,
      heapUsed: 20793384,
      external: 3170117,
      arrayBuffers: 62338
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1673119.671 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 24,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:18:27'
}
{
  error: Error: Cannot find module '@database/client'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts:3:24
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:24\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:24\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 08:18:52 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 4120,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 93908992,
      heapTotal: 35487744,
      heapUsed: 20482136,
      external: 3170117,
      arrayBuffers: 62338
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1673144.593 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 24,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:18:52'
}
{
  error: Error: Cannot find module '@database/client'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts:3:24
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:24\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:24\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 08:19:00 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 35760,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 93638656,
      heapTotal: 35487744,
      heapUsed: 20730760,
      external: 3170117,
      arrayBuffers: 62338
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1673153.156 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 24,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:19:00'
}
{
  error: Error: Cannot find module '@database/client'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts:3:24
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:24\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module '@database/client'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:24\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 08:19:10 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 3216,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 103743488,
      heapTotal: 35749888,
      heapUsed: 20888104,
      external: 3200526,
      arrayBuffers: 92795
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1673162.656 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 24,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:19:10'
}
{
  error: Error: Cannot find module 'C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\@piknowkyo\database\dist\index.js'
      at createEsmNotFoundErr (node:internal/modules/cjs/loader:1286:15)
      at finalizeEsmResolution (node:internal/modules/cjs/loader:1274:15)
      at resolveExports (node:internal/modules/cjs/loader:640:14)
      at Function._findPath (node:internal/modules/cjs/loader:748:31)
      at Function.<anonymous> (node:internal/modules/cjs/loader:1235:27)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12) {
    code: 'MODULE_NOT_FOUND',
    path: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\@piknowkyo\\database\\package.json'
  },
  level: 'error',
  message: "uncaughtException: Cannot find module 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\@piknowkyo\\database\\dist\\index.js'\n" +
    "Error: Cannot find module 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\@piknowkyo\\database\\dist\\index.js'\n" +
    '    at createEsmNotFoundErr (node:internal/modules/cjs/loader:1286:15)\n' +
    '    at finalizeEsmResolution (node:internal/modules/cjs/loader:1274:15)\n' +
    '    at resolveExports (node:internal/modules/cjs/loader:640:14)\n' +
    '    at Function._findPath (node:internal/modules/cjs/loader:748:31)\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1235:27)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)',
  stack: "Error: Cannot find module 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\@piknowkyo\\database\\dist\\index.js'\n" +
    '    at createEsmNotFoundErr (node:internal/modules/cjs/loader:1286:15)\n' +
    '    at finalizeEsmResolution (node:internal/modules/cjs/loader:1274:15)\n' +
    '    at resolveExports (node:internal/modules/cjs/loader:640:14)\n' +
    '    at Function._findPath (node:internal/modules/cjs/loader:748:31)\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1235:27)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)',
  exception: true,
  date: 'Mon Jul 28 2025 08:19:36 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 31560,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 103620608,
      heapTotal: 35487744,
      heapUsed: 20865472,
      external: 3197189,
      arrayBuffers: 89458
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1673188.593 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: 'createEsmNotFoundErr',
      line: 1286,
      method: null,
      native: false
    },
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: 'finalizeEsmResolution',
      line: 1274,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'resolveExports',
      line: 640,
      method: null,
      native: false
    },
    {
      column: 31,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._findPath',
      line: 748,
      method: '_findPath',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1235,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:19:36'
}
{
  error: Error: Cannot find module './generated'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:4:21
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 08:20:07 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 27620,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 103944192,
      heapTotal: 35749888,
      heapUsed: 20901288,
      external: 3213783,
      arrayBuffers: 106183
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1673219.375 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 21,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 4,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:20:07'
}
{
  error: Error: Cannot find module './generated'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:4:21
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 08:20:15 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 26708,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 93720576,
      heapTotal: 35749888,
      heapUsed: 20656632,
      external: 3170117,
      arrayBuffers: 62461
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1673227.265 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 21,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 4,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:20:15'
}
{
  error: Error: Cannot find module './generated'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:4:21
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 08:20:30 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 26264,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 93560832,
      heapTotal: 35749888,
      heapUsed: 20406776,
      external: 3170117,
      arrayBuffers: 62461
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1673242.296 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 21,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 4,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:20:30'
}
{
  error: Error: Cannot find module './generated'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:4:21
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 08:20:50 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 25344,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 93364224,
      heapTotal: 35487744,
      heapUsed: 20707296,
      external: 3170117,
      arrayBuffers: 62461
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1673262.843 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 21,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 4,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:20:50'
}
{
  error: Error: Cannot find module './generated'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:4:21
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 08:20:59 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 15476,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 93155328,
      heapTotal: 35749888,
      heapUsed: 20511928,
      external: 3170117,
      arrayBuffers: 62461
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1673271.546 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 21,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 4,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:20:59'
}
{
  error: Error: Cannot find module './generated'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:4:21
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 08:21:18 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 35512,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 93179904,
      heapTotal: 35487744,
      heapUsed: 20684016,
      external: 3170117,
      arrayBuffers: 62461
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1673290.64 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 21,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 4,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:21:18'
}
{
  error: Error: Cannot find module './generated'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:4:21
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 08:21:27 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 29256,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 93503488,
      heapTotal: 35749888,
      heapUsed: 20703264,
      external: 3170117,
      arrayBuffers: 62461
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1673299.859 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 21,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 4,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:21:27'
}
{
  error: Error: Cannot find module './generated'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:4:21
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 08:21:43 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 28692,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 93696000,
      heapTotal: 35749888,
      heapUsed: 20686136,
      external: 3170117,
      arrayBuffers: 62461
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1673315.546 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 21,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 4,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:21:43'
}
{
  error: Error: Cannot find module './generated'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:4:21
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 08:22:11 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 13356,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 93327360,
      heapTotal: 35749888,
      heapUsed: 20433104,
      external: 3170117,
      arrayBuffers: 62461
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1673343.75 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 21,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 4,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:22:11'
}
{
  error: Error: Cannot find module './generated'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:4:21
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 08:22:29 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 3432,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 93265920,
      heapTotal: 35487744,
      heapUsed: 20682256,
      external: 3170117,
      arrayBuffers: 62461
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1673361.359 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 21,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 4,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:22:29'
}
{
  error: Error: Cannot find module './generated'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:4:21
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 08:23:34 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 32300,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 93196288,
      heapTotal: 35487744,
      heapUsed: 20974328,
      external: 3170117,
      arrayBuffers: 62461
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1673426.828 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 21,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 4,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:23:34'
}
{
  error: Error: Cannot find module './generated'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:4:21
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 08:23:49 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 8360,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 93396992,
      heapTotal: 35487744,
      heapUsed: 20446288,
      external: 3170117,
      arrayBuffers: 62461
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1673441.609 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 21,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 4,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:23:49'
}
{
  error: Error: Cannot find module './generated'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:4:21
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 08:24:03 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 21640,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 92917760,
      heapTotal: 35487744,
      heapUsed: 20722280,
      external: 3170117,
      arrayBuffers: 62461
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1673456.109 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 21,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 4,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:24:03'
}
{
  error: Error: Cannot find module './generated'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:4:21
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 08:24:21 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 6868,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 93855744,
      heapTotal: 35487744,
      heapUsed: 20681344,
      external: 3170117,
      arrayBuffers: 62461
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1673473.812 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 21,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 4,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:24:21'
}
{
  error: Error: Cannot find module './generated'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:4:21
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 08:24:33 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 10612,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 93089792,
      heapTotal: 35749888,
      heapUsed: 20738552,
      external: 3170117,
      arrayBuffers: 62461
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1673485.953 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 21,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 4,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:24:33'
}
{
  error: Error: Cannot find module './generated'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:4:21
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 08:24:45 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 8940,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 93335552,
      heapTotal: 36012032,
      heapUsed: 20672384,
      external: 3170117,
      arrayBuffers: 62461
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1673497.687 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 21,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 4,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:24:45'
}
{
  error: Error: Cannot find module './generated'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:4:21
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 08:25:44 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 30760,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 92811264,
      heapTotal: 35749888,
      heapUsed: 20723288,
      external: 3170117,
      arrayBuffers: 62461
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1673556.234 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 21,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 4,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:25:44'
}
{
  error: Error: Cannot find module './generated'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:4:21
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 08:25:56 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 31200,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 93868032,
      heapTotal: 35749888,
      heapUsed: 20459352,
      external: 3170117,
      arrayBuffers: 62461
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1673568.703 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 21,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 4,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:25:56'
}
{
  error: Error: Cannot find module './generated'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:4:21
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 08:29:42 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 30408,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 88031232,
      heapTotal: 35487744,
      heapUsed: 20643552,
      external: 3170117,
      arrayBuffers: 62461
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1673794.234 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 21,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 4,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:29:42'
}
{
  error: Error: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.
      at new PrismaClient (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\.prisma\client\default.js:43:11)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:5:41
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14)
      at Object.j (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1197)
      at Module.load (node:internal/modules/cjs/loader:1318:32)
      at Function._load (node:internal/modules/cjs/loader:1128:12)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12),
  level: 'error',
  message: 'uncaughtException: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.\n' +
    'Error: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.\n' +
    '    at new PrismaClient (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\.prisma\\client\\default.js:43:11)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:5:41\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)',
  stack: 'Error: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.\n' +
    '    at new PrismaClient (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\.prisma\\client\\default.js:43:11)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:5:41\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)',
  exception: true,
  date: 'Mon Jul 28 2025 08:30:48 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 34984,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 103301120,
      heapTotal: 35749888,
      heapUsed: 21040976,
      external: 3225127,
      arrayBuffers: 117793
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1673860.984 },
  trace: [
    {
      column: 11,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\.prisma\\client\\default.js',
      function: 'new PrismaClient',
      line: 43,
      method: null,
      native: false
    },
    {
      column: 41,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 5,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    },
    {
      column: 1197,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Object.j',
      line: 1,
      method: 'j',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1318,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1128,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:30:48'
}
{
  error: Error: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.
      at new PrismaClient (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\.prisma\client\default.js:43:11)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:5:41
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14)
      at Object.j (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1197)
      at Module.load (node:internal/modules/cjs/loader:1318:32)
      at Function._load (node:internal/modules/cjs/loader:1128:12)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12),
  level: 'error',
  message: 'uncaughtException: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.\n' +
    'Error: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.\n' +
    '    at new PrismaClient (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\.prisma\\client\\default.js:43:11)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:5:41\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)',
  stack: 'Error: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.\n' +
    '    at new PrismaClient (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\.prisma\\client\\default.js:43:11)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:5:41\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)',
  exception: true,
  date: 'Mon Jul 28 2025 08:31:14 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 9704,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 93442048,
      heapTotal: 36012032,
      heapUsed: 20719584,
      external: 3170117,
      arrayBuffers: 62719
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1673886.39 },
  trace: [
    {
      column: 11,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\.prisma\\client\\default.js',
      function: 'new PrismaClient',
      line: 43,
      method: null,
      native: false
    },
    {
      column: 41,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 5,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    },
    {
      column: 1197,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Object.j',
      line: 1,
      method: 'j',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1318,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1128,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:31:14'
}
{
  error: Error: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.
      at new PrismaClient (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\.prisma\client\default.js:43:11)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:5:41
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14)
      at Object.j (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1197)
      at Module.load (node:internal/modules/cjs/loader:1318:32)
      at Function._load (node:internal/modules/cjs/loader:1128:12)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12),
  level: 'error',
  message: 'uncaughtException: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.\n' +
    'Error: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.\n' +
    '    at new PrismaClient (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\.prisma\\client\\default.js:43:11)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:5:41\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)',
  stack: 'Error: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.\n' +
    '    at new PrismaClient (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\.prisma\\client\\default.js:43:11)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:5:41\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)',
  exception: true,
  date: 'Mon Jul 28 2025 08:37:16 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 24452,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 104968192,
      heapTotal: 35487744,
      heapUsed: 21260800,
      external: 3321800,
      arrayBuffers: 214410
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1674248.703 },
  trace: [
    {
      column: 11,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\.prisma\\client\\default.js',
      function: 'new PrismaClient',
      line: 43,
      method: null,
      native: false
    },
    {
      column: 41,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 5,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    },
    {
      column: 1197,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Object.j',
      line: 1,
      method: 'j',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1318,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1128,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:37:16'
}
{
  error: Error: Cannot find module './generated'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:4:21
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 08:37:36 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 10500,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 93343744,
      heapTotal: 36012032,
      heapUsed: 20740856,
      external: 3170117,
      arrayBuffers: 62461
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1674268.406 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 21,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 4,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:37:36'
}
{
  error: Error: Cannot find module './generated'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\middleware\auth.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:4:21
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\packages\database\dist\client.js:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module './generated'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\middleware\\auth.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:4:21\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 08:39:00 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 27632,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 92622848,
      heapTotal: 35749888,
      heapUsed: 20663704,
      external: 3170117,
      arrayBuffers: 62461
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1674352.218 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 21,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 4,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\packages\\database\\dist\\client.js',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:39:00'
}
{
  error: Error: Transform failed with 1 error:
  C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\settings.ts:288:14: ERROR: Expected ";" but found ":"
      at failureErrorWithLog (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\esbuild\lib\main.js:1649:15)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\esbuild\lib\main.js:847:29
      at responseCallbacks.<computed> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\esbuild\lib\main.js:703:9)
      at handleIncomingPacket (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\esbuild\lib\main.js:762:9)
      at Socket.readFromStdout (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\esbuild\lib\main.js:679:7)
      at Socket.emit (node:events:518:28)
      at addChunk (node:internal/streams/readable:561:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
      at Readable.push (node:internal/streams/readable:392:5)
      at Pipe.onStreamRead (node:internal/stream_base_commons:189:23) {
    errors: [
      {
        detail: undefined,
        id: '',
        location: {
          column: 14,
          file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\settings.ts',
          length: 1,
          line: 288,
          lineText: '          name: true,',
          namespace: '',
          suggestion: ';'
        },
        notes: [],
        pluginName: '',
        text: 'Expected ";" but found ":"'
      }
    ],
    warnings: []
  },
  level: 'error',
  message: 'uncaughtException: Transform failed with 1 error:\n' +
    'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\settings.ts:288:14: ERROR: Expected ";" but found ":"\n' +
    'Error: Transform failed with 1 error:\n' +
    'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\settings.ts:288:14: ERROR: Expected ";" but found ":"\n' +
    '    at failureErrorWithLog (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\esbuild\\lib\\main.js:1649:15)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\esbuild\\lib\\main.js:847:29\n' +
    '    at responseCallbacks.<computed> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\esbuild\\lib\\main.js:703:9)\n' +
    '    at handleIncomingPacket (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\esbuild\\lib\\main.js:762:9)\n' +
    '    at Socket.readFromStdout (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\esbuild\\lib\\main.js:679:7)\n' +
    '    at Socket.emit (node:events:518:28)\n' +
    '    at addChunk (node:internal/streams/readable:561:12)\n' +
    '    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n' +
    '    at Readable.push (node:internal/streams/readable:392:5)\n' +
    '    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)',
  stack: 'Error: Transform failed with 1 error:\n' +
    'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\settings.ts:288:14: ERROR: Expected ";" but found ":"\n' +
    '    at failureErrorWithLog (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\esbuild\\lib\\main.js:1649:15)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\esbuild\\lib\\main.js:847:29\n' +
    '    at responseCallbacks.<computed> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\esbuild\\lib\\main.js:703:9)\n' +
    '    at handleIncomingPacket (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\esbuild\\lib\\main.js:762:9)\n' +
    '    at Socket.readFromStdout (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\esbuild\\lib\\main.js:679:7)\n' +
    '    at Socket.emit (node:events:518:28)\n' +
    '    at addChunk (node:internal/streams/readable:561:12)\n' +
    '    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n' +
    '    at Readable.push (node:internal/streams/readable:392:5)\n' +
    '    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)',
  exception: true,
  date: 'Mon Jul 28 2025 08:42:43 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 20572,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 125681664,
      heapTotal: 59641856,
      heapUsed: 40205648,
      external: 4862083,
      arrayBuffers: 139759
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1674576.125 },
  trace: [
    {
      column: 15,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\esbuild\\lib\\main.js',
      function: 'failureErrorWithLog',
      line: 1649,
      method: null,
      native: false
    },
    {
      column: 29,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\esbuild\\lib\\main.js',
      function: null,
      line: 847,
      method: null,
      native: false
    },
    {
      column: 9,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\esbuild\\lib\\main.js',
      function: 'responseCallbacks.<computed>',
      line: 703,
      method: '<computed>',
      native: false
    },
    {
      column: 9,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\esbuild\\lib\\main.js',
      function: 'handleIncomingPacket',
      line: 762,
      method: null,
      native: false
    },
    {
      column: 7,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\esbuild\\lib\\main.js',
      function: 'Socket.readFromStdout',
      line: 679,
      method: 'readFromStdout',
      native: false
    },
    {
      column: 28,
      file: 'node:events',
      function: 'Socket.emit',
      line: 518,
      method: 'emit',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/streams/readable',
      function: 'addChunk',
      line: 561,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'node:internal/streams/readable',
      function: 'readableAddChunkPushByteMode',
      line: 512,
      method: null,
      native: false
    },
    {
      column: 5,
      file: 'node:internal/streams/readable',
      function: 'Readable.push',
      line: 392,
      method: 'push',
      native: false
    },
    {
      column: 23,
      file: 'node:internal/stream_base_commons',
      function: 'Pipe.onStreamRead',
      line: 189,
      method: 'onStreamRead',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:42:43'
}
{
  error: Error: Transform failed with 1 error:
  C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\settings.ts:347:14: ERROR: Expected ";" but found ":"
      at failureErrorWithLog (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\esbuild\lib\main.js:1649:15)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\esbuild\lib\main.js:847:29
      at responseCallbacks.<computed> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\esbuild\lib\main.js:703:9)
      at handleIncomingPacket (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\esbuild\lib\main.js:762:9)
      at Socket.readFromStdout (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\esbuild\lib\main.js:679:7)
      at Socket.emit (node:events:518:28)
      at addChunk (node:internal/streams/readable:561:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
      at Readable.push (node:internal/streams/readable:392:5)
      at Pipe.onStreamRead (node:internal/stream_base_commons:189:23) {
    errors: [
      {
        detail: undefined,
        id: '',
        location: {
          column: 14,
          file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\settings.ts',
          length: 1,
          line: 347,
          lineText: '          name: true,',
          namespace: '',
          suggestion: ';'
        },
        notes: [],
        pluginName: '',
        text: 'Expected ";" but found ":"'
      }
    ],
    warnings: []
  },
  level: 'error',
  message: 'uncaughtException: Transform failed with 1 error:\n' +
    'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\settings.ts:347:14: ERROR: Expected ";" but found ":"\n' +
    'Error: Transform failed with 1 error:\n' +
    'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\settings.ts:347:14: ERROR: Expected ";" but found ":"\n' +
    '    at failureErrorWithLog (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\esbuild\\lib\\main.js:1649:15)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\esbuild\\lib\\main.js:847:29\n' +
    '    at responseCallbacks.<computed> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\esbuild\\lib\\main.js:703:9)\n' +
    '    at handleIncomingPacket (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\esbuild\\lib\\main.js:762:9)\n' +
    '    at Socket.readFromStdout (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\esbuild\\lib\\main.js:679:7)\n' +
    '    at Socket.emit (node:events:518:28)\n' +
    '    at addChunk (node:internal/streams/readable:561:12)\n' +
    '    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n' +
    '    at Readable.push (node:internal/streams/readable:392:5)\n' +
    '    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)',
  stack: 'Error: Transform failed with 1 error:\n' +
    'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\settings.ts:347:14: ERROR: Expected ";" but found ":"\n' +
    '    at failureErrorWithLog (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\esbuild\\lib\\main.js:1649:15)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\esbuild\\lib\\main.js:847:29\n' +
    '    at responseCallbacks.<computed> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\esbuild\\lib\\main.js:703:9)\n' +
    '    at handleIncomingPacket (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\esbuild\\lib\\main.js:762:9)\n' +
    '    at Socket.readFromStdout (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\esbuild\\lib\\main.js:679:7)\n' +
    '    at Socket.emit (node:events:518:28)\n' +
    '    at addChunk (node:internal/streams/readable:561:12)\n' +
    '    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n' +
    '    at Readable.push (node:internal/streams/readable:392:5)\n' +
    '    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)',
  exception: true,
  date: 'Mon Jul 28 2025 08:43:20 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 23160,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 125665280,
      heapTotal: 59904000,
      heapUsed: 39977392,
      external: 4862083,
      arrayBuffers: 139759
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1674612.64 },
  trace: [
    {
      column: 15,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\esbuild\\lib\\main.js',
      function: 'failureErrorWithLog',
      line: 1649,
      method: null,
      native: false
    },
    {
      column: 29,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\esbuild\\lib\\main.js',
      function: null,
      line: 847,
      method: null,
      native: false
    },
    {
      column: 9,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\esbuild\\lib\\main.js',
      function: 'responseCallbacks.<computed>',
      line: 703,
      method: '<computed>',
      native: false
    },
    {
      column: 9,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\esbuild\\lib\\main.js',
      function: 'handleIncomingPacket',
      line: 762,
      method: null,
      native: false
    },
    {
      column: 7,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\esbuild\\lib\\main.js',
      function: 'Socket.readFromStdout',
      line: 679,
      method: 'readFromStdout',
      native: false
    },
    {
      column: 28,
      file: 'node:events',
      function: 'Socket.emit',
      line: 518,
      method: 'emit',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/streams/readable',
      function: 'addChunk',
      line: 561,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'node:internal/streams/readable',
      function: 'readableAddChunkPushByteMode',
      line: 512,
      method: null,
      native: false
    },
    {
      column: 5,
      file: 'node:internal/streams/readable',
      function: 'Readable.push',
      line: 392,
      method: 'push',
      native: false
    },
    {
      column: 23,
      file: 'node:internal/stream_base_commons',
      function: 'Pipe.onStreamRead',
      line: 189,
      method: 'onStreamRead',
      native: false
    }
  ],
  timestamp: '2025-07-28 08:43:20'
}
{
  error: Error: listen EADDRINUSE: address already in use 0.0.0.0:3001
      at Server.setupListenHandle [as _listen2] (node:net:1907:16)
      at listenInCluster (node:net:1964:12)
      at node:net:2170:7
      at processTicksAndRejections (node:internal/process/task_queues:90:21) {
    code: 'EADDRINUSE',
    errno: -4091,
    syscall: 'listen',
    address: '0.0.0.0',
    port: 3001
  },
  level: 'error',
  message: 'uncaughtException: listen EADDRINUSE: address already in use 0.0.0.0:3001\n' +
    'Error: listen EADDRINUSE: address already in use 0.0.0.0:3001\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1907:16)\n' +
    '    at listenInCluster (node:net:1964:12)\n' +
    '    at node:net:2170:7\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:90:21)',
  stack: 'Error: listen EADDRINUSE: address already in use 0.0.0.0:3001\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1907:16)\n' +
    '    at listenInCluster (node:net:1964:12)\n' +
    '    at node:net:2170:7\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:90:21)',
  exception: true,
  date: 'Mon Jul 28 2025 09:13:59 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 5632,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 122335232,
      heapTotal: 62263296,
      heapUsed: 32200704,
      external: 4893851,
      arrayBuffers: 139875
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1676451.75 },
  trace: [
    {
      column: 16,
      file: 'node:net',
      function: 'Server.setupListenHandle [as _listen2]',
      line: 1907,
      method: 'setupListenHandle [as _listen2]',
      native: false
    },
    {
      column: 12,
      file: 'node:net',
      function: 'listenInCluster',
      line: 1964,
      method: null,
      native: false
    },
    {
      column: 7,
      file: 'node:net',
      function: null,
      line: 2170,
      method: null,
      native: false
    },
    {
      column: 21,
      file: 'node:internal/process/task_queues',
      function: 'processTicksAndRejections',
      line: 90,
      method: null,
      native: false
    }
  ],
  timestamp: '2025-07-28 09:13:59'
}
{
  error: Error: listen EADDRINUSE: address already in use 0.0.0.0:3001
      at Server.setupListenHandle [as _listen2] (node:net:1907:16)
      at listenInCluster (node:net:1964:12)
      at node:net:2170:7
      at processTicksAndRejections (node:internal/process/task_queues:90:21) {
    code: 'EADDRINUSE',
    errno: -4091,
    syscall: 'listen',
    address: '0.0.0.0',
    port: 3001
  },
  level: 'error',
  message: 'uncaughtException: listen EADDRINUSE: address already in use 0.0.0.0:3001\n' +
    'Error: listen EADDRINUSE: address already in use 0.0.0.0:3001\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1907:16)\n' +
    '    at listenInCluster (node:net:1964:12)\n' +
    '    at node:net:2170:7\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:90:21)',
  stack: 'Error: listen EADDRINUSE: address already in use 0.0.0.0:3001\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1907:16)\n' +
    '    at listenInCluster (node:net:1964:12)\n' +
    '    at node:net:2170:7\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:90:21)',
  exception: true,
  date: 'Mon Jul 28 2025 10:01:44 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 12696,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 123023360,
      heapTotal: 62263296,
      heapUsed: 32937472,
      external: 4893851,
      arrayBuffers: 139875
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1679316.843 },
  trace: [
    {
      column: 16,
      file: 'node:net',
      function: 'Server.setupListenHandle [as _listen2]',
      line: 1907,
      method: 'setupListenHandle [as _listen2]',
      native: false
    },
    {
      column: 12,
      file: 'node:net',
      function: 'listenInCluster',
      line: 1964,
      method: null,
      native: false
    },
    {
      column: 7,
      file: 'node:net',
      function: null,
      line: 2170,
      method: null,
      native: false
    },
    {
      column: 21,
      file: 'node:internal/process/task_queues',
      function: 'processTicksAndRejections',
      line: 90,
      method: null,
      native: false
    }
  ],
  timestamp: '2025-07-28 10:01:44'
}
{
  error: Error: listen EADDRINUSE: address already in use 0.0.0.0:3001
      at Server.setupListenHandle [as _listen2] (node:net:1907:16)
      at listenInCluster (node:net:1964:12)
      at node:net:2170:7
      at processTicksAndRejections (node:internal/process/task_queues:90:21) {
    code: 'EADDRINUSE',
    errno: -4091,
    syscall: 'listen',
    address: '0.0.0.0',
    port: 3001
  },
  level: 'error',
  message: 'uncaughtException: listen EADDRINUSE: address already in use 0.0.0.0:3001\n' +
    'Error: listen EADDRINUSE: address already in use 0.0.0.0:3001\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1907:16)\n' +
    '    at listenInCluster (node:net:1964:12)\n' +
    '    at node:net:2170:7\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:90:21)',
  stack: 'Error: listen EADDRINUSE: address already in use 0.0.0.0:3001\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1907:16)\n' +
    '    at listenInCluster (node:net:1964:12)\n' +
    '    at node:net:2170:7\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:90:21)',
  exception: true,
  date: 'Mon Jul 28 2025 10:04:06 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 19660,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 122298368,
      heapTotal: 62263296,
      heapUsed: 32916184,
      external: 4893851,
      arrayBuffers: 139875
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1679458.14 },
  trace: [
    {
      column: 16,
      file: 'node:net',
      function: 'Server.setupListenHandle [as _listen2]',
      line: 1907,
      method: 'setupListenHandle [as _listen2]',
      native: false
    },
    {
      column: 12,
      file: 'node:net',
      function: 'listenInCluster',
      line: 1964,
      method: null,
      native: false
    },
    {
      column: 7,
      file: 'node:net',
      function: null,
      line: 2170,
      method: null,
      native: false
    },
    {
      column: 21,
      file: 'node:internal/process/task_queues',
      function: 'processTicksAndRejections',
      line: 90,
      method: null,
      native: false
    }
  ],
  timestamp: '2025-07-28 10:04:06'
}
{
  error: Error: listen EADDRINUSE: address already in use 0.0.0.0:5000
      at Server.setupListenHandle [as _listen2] (node:net:1907:16)
      at listenInCluster (node:net:1964:12)
      at node:net:2170:7
      at processTicksAndRejections (node:internal/process/task_queues:90:21) {
    code: 'EADDRINUSE',
    errno: -4091,
    syscall: 'listen',
    address: '0.0.0.0',
    port: 5000
  },
  level: 'error',
  message: 'uncaughtException: listen EADDRINUSE: address already in use 0.0.0.0:5000\n' +
    'Error: listen EADDRINUSE: address already in use 0.0.0.0:5000\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1907:16)\n' +
    '    at listenInCluster (node:net:1964:12)\n' +
    '    at node:net:2170:7\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:90:21)',
  stack: 'Error: listen EADDRINUSE: address already in use 0.0.0.0:5000\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1907:16)\n' +
    '    at listenInCluster (node:net:1964:12)\n' +
    '    at node:net:2170:7\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:90:21)',
  exception: true,
  date: 'Mon Jul 28 2025 13:12:28 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 29720,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 115879936,
      heapTotal: 59535360,
      heapUsed: 40072504,
      external: 4893851,
      arrayBuffers: 139716
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1690760.375 },
  trace: [
    {
      column: 16,
      file: 'node:net',
      function: 'Server.setupListenHandle [as _listen2]',
      line: 1907,
      method: 'setupListenHandle [as _listen2]',
      native: false
    },
    {
      column: 12,
      file: 'node:net',
      function: 'listenInCluster',
      line: 1964,
      method: null,
      native: false
    },
    {
      column: 7,
      file: 'node:net',
      function: null,
      line: 2170,
      method: null,
      native: false
    },
    {
      column: 21,
      file: 'node:internal/process/task_queues',
      function: 'processTicksAndRejections',
      line: 90,
      method: null,
      native: false
    }
  ],
  timestamp: '2025-07-28 13:12:28'
}
{
  error: Error: Cannot find module '../utils/error-handler'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts:7:29
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module '../utils/error-handler'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module '../utils/error-handler'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:7:29\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module '../utils/error-handler'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:7:29\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 14:24:04 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 7572,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 123088896,
      heapTotal: 59797504,
      heapUsed: 36464760,
      external: 4921699,
      arrayBuffers: 198964
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1695055.906 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 29,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      function: null,
      line: 7,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 14:24:04'
}
{
  error: Error: Cannot find module '../utils/error-handler'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts:7:29
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module '../utils/error-handler'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module '../utils/error-handler'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:7:29\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module '../utils/error-handler'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:7:29\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 14:26:04 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 27624,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 121454592,
      heapTotal: 59535360,
      heapUsed: 36037448,
      external: 4874805,
      arrayBuffers: 152062
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1695175.921 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 29,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      function: null,
      line: 7,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 14:26:04'
}
{
  error: Error: Cannot find module '../utils/error-handler'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts:7:29
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module '../utils/error-handler'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module '../utils/error-handler'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:7:29\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module '../utils/error-handler'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:7:29\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 14:26:13 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 28544,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 121528320,
      heapTotal: 59535360,
      heapUsed: 35994984,
      external: 4875181,
      arrayBuffers: 152438
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1695184.984 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 29,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      function: null,
      line: 7,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 14:26:13'
}
{
  error: Error: Cannot find module '../utils/error-handler'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts:7:29
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module '../utils/error-handler'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module '../utils/error-handler'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:7:29\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module '../utils/error-handler'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:7:29\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 14:26:49 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 5052,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 111222784,
      heapTotal: 59011072,
      heapUsed: 35842664,
      external: 4862083,
      arrayBuffers: 139332
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1695221.828 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 29,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      function: null,
      line: 7,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 14:26:49'
}
{
  error: Error: Cannot find module '../utils/error-handler'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts:7:29
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module '../utils/error-handler'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module '../utils/error-handler'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:7:29\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module '../utils/error-handler'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:7:29\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 14:26:57 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 11636,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 124096512,
      heapTotal: 59011072,
      heapUsed: 36909256,
      external: 5043110,
      arrayBuffers: 320367
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1695229.89 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 29,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      function: null,
      line: 7,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 14:26:57'
}
{
  error: Error: Cannot find module '../utils/error-handler'
  Require stack:
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts
  - C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\index.ts
      at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)
      at Function.l.default._resolveFilename (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1671)
      at Function._load (node:internal/modules/cjs/loader:1075:27)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts:7:29
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module '../utils/error-handler'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    "Error: Cannot find module '../utils/error-handler'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:7:29\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  stack: "Error: Cannot find module '../utils/error-handler'\n" +
    'Require stack:\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts\n' +
    '- C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts\n' +
    '    at Function.<anonymous> (node:internal/modules/cjs/loader:1249:15)\n' +
    '    at Function.l.default._resolveFilename (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1671)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1075:27)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:7:29\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)',
  exception: true,
  date: 'Mon Jul 28 2025 14:27:16 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 9312,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 111853568,
      heapTotal: 59273216,
      heapUsed: 36146824,
      external: 4862083,
      arrayBuffers: 139332
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1695247.921 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: null,
      line: 1249,
      method: null,
      native: false
    },
    {
      column: 1671,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Function.l.default._resolveFilename',
      line: 1,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1075,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    },
    {
      column: 29,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      function: null,
      line: 7,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    }
  ],
  timestamp: '2025-07-28 14:27:16'
}
{
  error: Error: Route.get() requires a callback function but got a [object Undefined]
      at Route.<computed> [as get] (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\express\lib\router\route.js:216:15)
      at Function.proto.<computed> [as get] (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\express\lib\router\index.js:521:19)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts:76:8
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14)
      at Object.j (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1197)
      at Module.load (node:internal/modules/cjs/loader:1318:32)
      at Function._load (node:internal/modules/cjs/loader:1128:12)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24),
  level: 'error',
  message: 'uncaughtException: Route.get() requires a callback function but got a [object Undefined]\n' +
    'Error: Route.get() requires a callback function but got a [object Undefined]\n' +
    '    at Route.<computed> [as get] (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\express\\lib\\router\\route.js:216:15)\n' +
    '    at Function.proto.<computed> [as get] (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\express\\lib\\router\\index.js:521:19)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:76:8\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)',
  stack: 'Error: Route.get() requires a callback function but got a [object Undefined]\n' +
    '    at Route.<computed> [as get] (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\express\\lib\\router\\route.js:216:15)\n' +
    '    at Function.proto.<computed> [as get] (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\express\\lib\\router\\index.js:521:19)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:76:8\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)',
  exception: true,
  date: 'Mon Jul 28 2025 14:28:58 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 30472,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 124567552,
      heapTotal: 59535360,
      heapUsed: 37963224,
      external: 4909064,
      arrayBuffers: 186321
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1695349.921 },
  trace: [
    {
      column: 15,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\express\\lib\\router\\route.js',
      function: 'Route.<computed> [as get]',
      line: 216,
      method: '<computed> [as get]',
      native: false
    },
    {
      column: 19,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\express\\lib\\router\\index.js',
      function: 'Function.proto.<computed> [as get]',
      line: 521,
      method: '<computed> [as get]',
      native: false
    },
    {
      column: 8,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      function: null,
      line: 76,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    },
    {
      column: 1197,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Object.j',
      line: 1,
      method: 'j',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1318,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1128,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    }
  ],
  timestamp: '2025-07-28 14:28:58'
}
{
  error: Error: Route.get() requires a callback function but got a [object Undefined]
      at Route.<computed> [as get] (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\express\lib\router\route.js:216:15)
      at Function.proto.<computed> [as get] (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\express\lib\router\index.js:521:19)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts:76:8
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14)
      at Object.j (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1197)
      at Module.load (node:internal/modules/cjs/loader:1318:32)
      at Function._load (node:internal/modules/cjs/loader:1128:12)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24),
  level: 'error',
  message: 'uncaughtException: Route.get() requires a callback function but got a [object Undefined]\n' +
    'Error: Route.get() requires a callback function but got a [object Undefined]\n' +
    '    at Route.<computed> [as get] (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\express\\lib\\router\\route.js:216:15)\n' +
    '    at Function.proto.<computed> [as get] (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\express\\lib\\router\\index.js:521:19)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:76:8\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)',
  stack: 'Error: Route.get() requires a callback function but got a [object Undefined]\n' +
    '    at Route.<computed> [as get] (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\express\\lib\\router\\route.js:216:15)\n' +
    '    at Function.proto.<computed> [as get] (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\express\\lib\\router\\index.js:521:19)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:76:8\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)',
  exception: true,
  date: 'Mon Jul 28 2025 14:28:58 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 8596,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 124055552,
      heapTotal: 59535360,
      heapUsed: 37782608,
      external: 4909064,
      arrayBuffers: 186321
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1695349.937 },
  trace: [
    {
      column: 15,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\express\\lib\\router\\route.js',
      function: 'Route.<computed> [as get]',
      line: 216,
      method: '<computed> [as get]',
      native: false
    },
    {
      column: 19,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\express\\lib\\router\\index.js',
      function: 'Function.proto.<computed> [as get]',
      line: 521,
      method: '<computed> [as get]',
      native: false
    },
    {
      column: 8,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      function: null,
      line: 76,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    },
    {
      column: 1197,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Object.j',
      line: 1,
      method: 'j',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1318,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1128,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    }
  ],
  timestamp: '2025-07-28 14:28:58'
}
{
  error: Error: Route.get() requires a callback function but got a [object Undefined]
      at Route.<computed> [as get] (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\express\lib\router\route.js:216:15)
      at Function.proto.<computed> [as get] (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\express\lib\router\index.js:521:19)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts:76:8
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14)
      at Object.j (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1197)
      at Module.load (node:internal/modules/cjs/loader:1318:32)
      at Function._load (node:internal/modules/cjs/loader:1128:12)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24),
  level: 'error',
  message: 'uncaughtException: Route.get() requires a callback function but got a [object Undefined]\n' +
    'Error: Route.get() requires a callback function but got a [object Undefined]\n' +
    '    at Route.<computed> [as get] (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\express\\lib\\router\\route.js:216:15)\n' +
    '    at Function.proto.<computed> [as get] (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\express\\lib\\router\\index.js:521:19)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:76:8\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)',
  stack: 'Error: Route.get() requires a callback function but got a [object Undefined]\n' +
    '    at Route.<computed> [as get] (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\express\\lib\\router\\route.js:216:15)\n' +
    '    at Function.proto.<computed> [as get] (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\express\\lib\\router\\index.js:521:19)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:76:8\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)',
  exception: true,
  date: 'Mon Jul 28 2025 14:29:28 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 1960,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 113545216,
      heapTotal: 59011072,
      heapUsed: 37447576,
      external: 4862083,
      arrayBuffers: 139332
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1695380.718 },
  trace: [
    {
      column: 15,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\express\\lib\\router\\route.js',
      function: 'Route.<computed> [as get]',
      line: 216,
      method: '<computed> [as get]',
      native: false
    },
    {
      column: 19,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\express\\lib\\router\\index.js',
      function: 'Function.proto.<computed> [as get]',
      line: 521,
      method: '<computed> [as get]',
      native: false
    },
    {
      column: 8,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      function: null,
      line: 76,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    },
    {
      column: 1197,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Object.j',
      line: 1,
      method: 'j',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1318,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1128,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    }
  ],
  timestamp: '2025-07-28 14:29:28'
}
{
  error: ReferenceError: requireAuth is not defined
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts:76:17
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14)
      at Object.j (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1197)
      at Module.load (node:internal/modules/cjs/loader:1318:32)
      at Function._load (node:internal/modules/cjs/loader:1128:12)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16),
  level: 'error',
  message: 'uncaughtException: requireAuth is not defined\n' +
    'ReferenceError: requireAuth is not defined\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:76:17\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)',
  stack: 'ReferenceError: requireAuth is not defined\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:76:17\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)',
  exception: true,
  date: 'Mon Jul 28 2025 14:30:11 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 33876,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 123793408,
      heapTotal: 59535360,
      heapUsed: 37434096,
      external: 4908953,
      arrayBuffers: 186210
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1695423.093 },
  trace: [
    {
      column: 17,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      function: null,
      line: 76,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    },
    {
      column: 1197,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Object.j',
      line: 1,
      method: 'j',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1318,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1128,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    }
  ],
  timestamp: '2025-07-28 14:30:11'
}
{
  error: ReferenceError: requireAuth is not defined
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts:76:17
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14)
      at Object.j (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1197)
      at Module.load (node:internal/modules/cjs/loader:1318:32)
      at Function._load (node:internal/modules/cjs/loader:1128:12)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16),
  level: 'error',
  message: 'uncaughtException: requireAuth is not defined\n' +
    'ReferenceError: requireAuth is not defined\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:76:17\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)',
  stack: 'ReferenceError: requireAuth is not defined\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:76:17\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)',
  exception: true,
  date: 'Mon Jul 28 2025 14:30:11 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 8356,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 123150336,
      heapTotal: 59535360,
      heapUsed: 37488152,
      external: 4908953,
      arrayBuffers: 186210
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1695423.093 },
  trace: [
    {
      column: 17,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      function: null,
      line: 76,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    },
    {
      column: 1197,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Object.j',
      line: 1,
      method: 'j',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1318,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1128,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    }
  ],
  timestamp: '2025-07-28 14:30:11'
}
{
  error: ReferenceError: requireAuth is not defined
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts:142:18
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14)
      at Object.j (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1197)
      at Module.load (node:internal/modules/cjs/loader:1318:32)
      at Function._load (node:internal/modules/cjs/loader:1128:12)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16),
  level: 'error',
  message: 'uncaughtException: requireAuth is not defined\n' +
    'ReferenceError: requireAuth is not defined\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:142:18\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)',
  stack: 'ReferenceError: requireAuth is not defined\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:142:18\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)',
  exception: true,
  date: 'Mon Jul 28 2025 14:30:21 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 15448,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 123613184,
      heapTotal: 59535360,
      heapUsed: 37480264,
      external: 4909035,
      arrayBuffers: 186292
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1695433.625 },
  trace: [
    {
      column: 18,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      function: null,
      line: 142,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    },
    {
      column: 1197,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Object.j',
      line: 1,
      method: 'j',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1318,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1128,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    }
  ],
  timestamp: '2025-07-28 14:30:21'
}
{
  error: ReferenceError: requireAuth is not defined
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts:142:18
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14)
      at Object.j (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1197)
      at Module.load (node:internal/modules/cjs/loader:1318:32)
      at Function._load (node:internal/modules/cjs/loader:1128:12)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16),
  level: 'error',
  message: 'uncaughtException: requireAuth is not defined\n' +
    'ReferenceError: requireAuth is not defined\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:142:18\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)',
  stack: 'ReferenceError: requireAuth is not defined\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:142:18\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)',
  exception: true,
  date: 'Mon Jul 28 2025 14:30:21 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 19204,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 123293696,
      heapTotal: 59273216,
      heapUsed: 37454488,
      external: 4909035,
      arrayBuffers: 186292
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1695433.64 },
  trace: [
    {
      column: 18,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      function: null,
      line: 142,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    },
    {
      column: 1197,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Object.j',
      line: 1,
      method: 'j',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1318,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1128,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    }
  ],
  timestamp: '2025-07-28 14:30:21'
}
{
  error: ReferenceError: requireAuth is not defined
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts:301:29
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14)
      at Object.j (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1197)
      at Module.load (node:internal/modules/cjs/loader:1318:32)
      at Function._load (node:internal/modules/cjs/loader:1128:12)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16),
  level: 'error',
  message: 'uncaughtException: requireAuth is not defined\n' +
    'ReferenceError: requireAuth is not defined\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:301:29\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)',
  stack: 'ReferenceError: requireAuth is not defined\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:301:29\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)',
  exception: true,
  date: 'Mon Jul 28 2025 14:30:36 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 14380,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 123490304,
      heapTotal: 59273216,
      heapUsed: 37461728,
      external: 4909055,
      arrayBuffers: 186312
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1695448.687 },
  trace: [
    {
      column: 29,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      function: null,
      line: 301,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    },
    {
      column: 1197,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Object.j',
      line: 1,
      method: 'j',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1318,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1128,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    }
  ],
  timestamp: '2025-07-28 14:30:36'
}
{
  error: ReferenceError: requireAuth is not defined
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts:301:29
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14)
      at Object.j (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1197)
      at Module.load (node:internal/modules/cjs/loader:1318:32)
      at Function._load (node:internal/modules/cjs/loader:1128:12)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16),
  level: 'error',
  message: 'uncaughtException: requireAuth is not defined\n' +
    'ReferenceError: requireAuth is not defined\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:301:29\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)',
  stack: 'ReferenceError: requireAuth is not defined\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:301:29\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)',
  exception: true,
  date: 'Mon Jul 28 2025 14:30:36 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 25280,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 123412480,
      heapTotal: 59273216,
      heapUsed: 37471248,
      external: 4909055,
      arrayBuffers: 186312
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1695448.687 },
  trace: [
    {
      column: 29,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      function: null,
      line: 301,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    },
    {
      column: 1197,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Object.j',
      line: 1,
      method: 'j',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1318,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1128,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    }
  ],
  timestamp: '2025-07-28 14:30:36'
}
{
  error: ReferenceError: requireAuth is not defined
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts:332:27
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14)
      at Object.j (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1197)
      at Module.load (node:internal/modules/cjs/loader:1318:32)
      at Function._load (node:internal/modules/cjs/loader:1128:12)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16),
  level: 'error',
  message: 'uncaughtException: requireAuth is not defined\n' +
    'ReferenceError: requireAuth is not defined\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:332:27\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)',
  stack: 'ReferenceError: requireAuth is not defined\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:332:27\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)',
  exception: true,
  date: 'Mon Jul 28 2025 14:30:59 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 2088,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 123142144,
      heapTotal: 59273216,
      heapUsed: 37447664,
      external: 4909075,
      arrayBuffers: 186332
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1695471.39 },
  trace: [
    {
      column: 27,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      function: null,
      line: 332,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    },
    {
      column: 1197,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Object.j',
      line: 1,
      method: 'j',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1318,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1128,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    }
  ],
  timestamp: '2025-07-28 14:30:59'
}
{
  error: ReferenceError: requireAuth is not defined
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts:332:27
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\ai-task-config.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14)
      at Object.j (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1197)
      at Module.load (node:internal/modules/cjs/loader:1318:32)
      at Function._load (node:internal/modules/cjs/loader:1128:12)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16),
  level: 'error',
  message: 'uncaughtException: requireAuth is not defined\n' +
    'ReferenceError: requireAuth is not defined\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:332:27\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)',
  stack: 'ReferenceError: requireAuth is not defined\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:332:27\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)',
  exception: true,
  date: 'Mon Jul 28 2025 14:30:59 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 12192,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 123457536,
      heapTotal: 59535360,
      heapUsed: 37466888,
      external: 4909075,
      arrayBuffers: 186332
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1695471.406 },
  trace: [
    {
      column: 27,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      function: null,
      line: 332,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\ai-task-config.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    },
    {
      column: 1197,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Object.j',
      line: 1,
      method: 'j',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1318,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1128,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    }
  ],
  timestamp: '2025-07-28 14:30:59'
}
{
  error: Error: Route.get() requires a callback function but got a [object Undefined]
      at Route.<computed> [as get] (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\express\lib\router\route.js:216:15)
      at Function.proto.<computed> [as get] (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\express\lib\router\index.js:521:19)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\video-generation-config.ts:79:8
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\video-generation-config.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14)
      at Object.j (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1197)
      at Module.load (node:internal/modules/cjs/loader:1318:32)
      at Function._load (node:internal/modules/cjs/loader:1128:12)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24),
  level: 'error',
  message: 'uncaughtException: Route.get() requires a callback function but got a [object Undefined]\n' +
    'Error: Route.get() requires a callback function but got a [object Undefined]\n' +
    '    at Route.<computed> [as get] (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\express\\lib\\router\\route.js:216:15)\n' +
    '    at Function.proto.<computed> [as get] (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\express\\lib\\router\\index.js:521:19)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts:79:8\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)',
  stack: 'Error: Route.get() requires a callback function but got a [object Undefined]\n' +
    '    at Route.<computed> [as get] (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\express\\lib\\router\\route.js:216:15)\n' +
    '    at Function.proto.<computed> [as get] (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\express\\lib\\router\\index.js:521:19)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts:79:8\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)',
  exception: true,
  date: 'Mon Jul 28 2025 14:31:14 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 23316,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 126509056,
      heapTotal: 59535360,
      heapUsed: 39280688,
      external: 4946830,
      arrayBuffers: 224237
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1695486.562 },
  trace: [
    {
      column: 15,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\express\\lib\\router\\route.js',
      function: 'Route.<computed> [as get]',
      line: 216,
      method: '<computed> [as get]',
      native: false
    },
    {
      column: 19,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\express\\lib\\router\\index.js',
      function: 'Function.proto.<computed> [as get]',
      line: 521,
      method: '<computed> [as get]',
      native: false
    },
    {
      column: 8,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts',
      function: null,
      line: 79,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    },
    {
      column: 1197,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Object.j',
      line: 1,
      method: 'j',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1318,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1128,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    }
  ],
  timestamp: '2025-07-28 14:31:14'
}
{
  error: Error: Route.get() requires a callback function but got a [object Undefined]
      at Route.<computed> [as get] (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\express\lib\router\route.js:216:15)
      at Function.proto.<computed> [as get] (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\express\lib\router\index.js:521:19)
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\video-generation-config.ts:79:8
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\video-generation-config.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14)
      at Object.j (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1197)
      at Module.load (node:internal/modules/cjs/loader:1318:32)
      at Function._load (node:internal/modules/cjs/loader:1128:12)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24),
  level: 'error',
  message: 'uncaughtException: Route.get() requires a callback function but got a [object Undefined]\n' +
    'Error: Route.get() requires a callback function but got a [object Undefined]\n' +
    '    at Route.<computed> [as get] (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\express\\lib\\router\\route.js:216:15)\n' +
    '    at Function.proto.<computed> [as get] (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\express\\lib\\router\\index.js:521:19)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts:79:8\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)',
  stack: 'Error: Route.get() requires a callback function but got a [object Undefined]\n' +
    '    at Route.<computed> [as get] (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\express\\lib\\router\\route.js:216:15)\n' +
    '    at Function.proto.<computed> [as get] (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\express\\lib\\router\\index.js:521:19)\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts:79:8\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)',
  exception: true,
  date: 'Mon Jul 28 2025 14:31:14 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 18916,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 127201280,
      heapTotal: 59535360,
      heapUsed: 39349496,
      external: 4946830,
      arrayBuffers: 224237
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1695486.578 },
  trace: [
    {
      column: 15,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\express\\lib\\router\\route.js',
      function: 'Route.<computed> [as get]',
      line: 216,
      method: '<computed> [as get]',
      native: false
    },
    {
      column: 19,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\express\\lib\\router\\index.js',
      function: 'Function.proto.<computed> [as get]',
      line: 521,
      method: '<computed> [as get]',
      native: false
    },
    {
      column: 8,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts',
      function: null,
      line: 79,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    },
    {
      column: 1197,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Object.j',
      line: 1,
      method: 'j',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1318,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1128,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    }
  ],
  timestamp: '2025-07-28 14:31:14'
}
{
  error: ReferenceError: requireAuth is not defined
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\video-generation-config.ts:79:17
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\video-generation-config.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14)
      at Object.j (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1197)
      at Module.load (node:internal/modules/cjs/loader:1318:32)
      at Function._load (node:internal/modules/cjs/loader:1128:12)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16),
  level: 'error',
  message: 'uncaughtException: requireAuth is not defined\n' +
    'ReferenceError: requireAuth is not defined\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts:79:17\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)',
  stack: 'ReferenceError: requireAuth is not defined\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts:79:17\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)',
  exception: true,
  date: 'Mon Jul 28 2025 14:31:25 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 8084,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 124014592,
      heapTotal: 59535360,
      heapUsed: 38346040,
      external: 4899720,
      arrayBuffers: 177119
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1695497.25 },
  trace: [
    {
      column: 17,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts',
      function: null,
      line: 79,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    },
    {
      column: 1197,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Object.j',
      line: 1,
      method: 'j',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1318,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1128,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    }
  ],
  timestamp: '2025-07-28 14:31:25'
}
{
  error: ReferenceError: requireAuth is not defined
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\video-generation-config.ts:79:17
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\video-generation-config.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14)
      at Object.j (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1197)
      at Module.load (node:internal/modules/cjs/loader:1318:32)
      at Function._load (node:internal/modules/cjs/loader:1128:12)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16),
  level: 'error',
  message: 'uncaughtException: requireAuth is not defined\n' +
    'ReferenceError: requireAuth is not defined\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts:79:17\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)',
  stack: 'ReferenceError: requireAuth is not defined\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts:79:17\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)',
  exception: true,
  date: 'Mon Jul 28 2025 14:31:25 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 25888,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 123727872,
      heapTotal: 59273216,
      heapUsed: 38131000,
      external: 4899720,
      arrayBuffers: 177119
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1695497.265 },
  trace: [
    {
      column: 17,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts',
      function: null,
      line: 79,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    },
    {
      column: 1197,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Object.j',
      line: 1,
      method: 'j',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1318,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1128,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    }
  ],
  timestamp: '2025-07-28 14:31:25'
}
{
  error: ReferenceError: requireAuth is not defined
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\video-generation-config.ts:148:18
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\video-generation-config.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14)
      at Object.j (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1197)
      at Module.load (node:internal/modules/cjs/loader:1318:32)
      at Function._load (node:internal/modules/cjs/loader:1128:12)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16),
  level: 'error',
  message: 'uncaughtException: requireAuth is not defined\n' +
    'ReferenceError: requireAuth is not defined\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts:148:18\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)',
  stack: 'ReferenceError: requireAuth is not defined\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts:148:18\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)',
  exception: true,
  date: 'Mon Jul 28 2025 14:31:40 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 21508,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 124489728,
      heapTotal: 59535360,
      heapUsed: 38352256,
      external: 4899802,
      arrayBuffers: 177201
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1695512.156 },
  trace: [
    {
      column: 18,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts',
      function: null,
      line: 148,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    },
    {
      column: 1197,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Object.j',
      line: 1,
      method: 'j',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1318,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1128,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    }
  ],
  timestamp: '2025-07-28 14:31:40'
}
{
  error: ReferenceError: requireAuth is not defined
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\video-generation-config.ts:148:18
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\video-generation-config.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14)
      at Object.j (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1197)
      at Module.load (node:internal/modules/cjs/loader:1318:32)
      at Function._load (node:internal/modules/cjs/loader:1128:12)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16),
  level: 'error',
  message: 'uncaughtException: requireAuth is not defined\n' +
    'ReferenceError: requireAuth is not defined\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts:148:18\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)',
  stack: 'ReferenceError: requireAuth is not defined\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts:148:18\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)',
  exception: true,
  date: 'Mon Jul 28 2025 14:31:40 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 31836,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 123650048,
      heapTotal: 59273216,
      heapUsed: 38346568,
      external: 4899802,
      arrayBuffers: 177201
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1695512.156 },
  trace: [
    {
      column: 18,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts',
      function: null,
      line: 148,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    },
    {
      column: 1197,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Object.j',
      line: 1,
      method: 'j',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1318,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1128,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    }
  ],
  timestamp: '2025-07-28 14:31:40'
}
{
  error: ReferenceError: requireAuth is not defined
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\video-generation-config.ts:254:20
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\video-generation-config.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14)
      at Object.j (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1197)
      at Module.load (node:internal/modules/cjs/loader:1318:32)
      at Function._load (node:internal/modules/cjs/loader:1128:12)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16),
  level: 'error',
  message: 'uncaughtException: requireAuth is not defined\n' +
    'ReferenceError: requireAuth is not defined\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts:254:20\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)',
  stack: 'ReferenceError: requireAuth is not defined\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts:254:20\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)',
  exception: true,
  date: 'Mon Jul 28 2025 14:31:50 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 5004,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 125034496,
      heapTotal: 59535360,
      heapUsed: 38368448,
      external: 4899822,
      arrayBuffers: 177221
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1695522.468 },
  trace: [
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts',
      function: null,
      line: 254,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    },
    {
      column: 1197,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Object.j',
      line: 1,
      method: 'j',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1318,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1128,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    }
  ],
  timestamp: '2025-07-28 14:31:50'
}
{
  error: ReferenceError: requireAuth is not defined
      at C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\video-generation-config.ts:254:20
      at Object.<anonymous> (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\apps\backend\src\routes\video-generation-config.ts:3:3)
      at Module._compile (node:internal/modules/cjs/loader:1546:14)
      at Object.j (C:\Users\<USER>\Documents\GitHub\piknowkyo-generator\node_modules\tsx\dist\cjs\index.cjs:1:1197)
      at Module.load (node:internal/modules/cjs/loader:1318:32)
      at Function._load (node:internal/modules/cjs/loader:1128:12)
      at TracingChannel.traceSync (node:diagnostics_channel:315:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)
      at Module.require (node:internal/modules/cjs/loader:1340:12)
      at require (node:internal/modules/helpers:141:16),
  level: 'error',
  message: 'uncaughtException: requireAuth is not defined\n' +
    'ReferenceError: requireAuth is not defined\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts:254:20\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)',
  stack: 'ReferenceError: requireAuth is not defined\n' +
    '    at C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts:254:20\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts:3:3)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n' +
    '    at Object.j (C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs:1:1197)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1318:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1128:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:218:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1340:12)\n' +
    '    at require (node:internal/modules/helpers:141:16)',
  exception: true,
  date: 'Mon Jul 28 2025 14:31:50 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 34692,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 124768256,
      heapTotal: 59535360,
      heapUsed: 38192528,
      external: 4899822,
      arrayBuffers: 177221
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1695522.468 },
  trace: [
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts',
      function: null,
      line: 254,
      method: null,
      native: false
    },
    {
      column: 3,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\routes\\video-generation-config.ts',
      function: null,
      line: 3,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1546,
      method: '_compile',
      native: false
    },
    {
      column: 1197,
      file: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\node_modules\\tsx\\dist\\cjs\\index.cjs',
      function: 'Object.j',
      line: 1,
      method: 'j',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1318,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1128,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 315,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 218,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1340,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 141,
      method: null,
      native: false
    }
  ],
  timestamp: '2025-07-28 14:31:50'
}
{
  error: Error: listen EADDRINUSE: address already in use 0.0.0.0:3000
      at Server.setupListenHandle [as _listen2] (node:net:1907:16)
      at listenInCluster (node:net:1964:12)
      at node:net:2170:7
      at processTicksAndRejections (node:internal/process/task_queues:90:21) {
    code: 'EADDRINUSE',
    errno: -4091,
    syscall: 'listen',
    address: '0.0.0.0',
    port: 3000
  },
  level: 'error',
  message: 'uncaughtException: listen EADDRINUSE: address already in use 0.0.0.0:3000\n' +
    'Error: listen EADDRINUSE: address already in use 0.0.0.0:3000\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1907:16)\n' +
    '    at listenInCluster (node:net:1964:12)\n' +
    '    at node:net:2170:7\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:90:21)',
  stack: 'Error: listen EADDRINUSE: address already in use 0.0.0.0:3000\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1907:16)\n' +
    '    at listenInCluster (node:net:1964:12)\n' +
    '    at node:net:2170:7\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:90:21)',
  exception: true,
  date: 'Mon Jul 28 2025 14:32:01 GMT-0400 (Eastern Daylight Time)',
  process: {
    pid: 26052,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.11.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Documents\\GitHub\\piknowkyo-generator\\apps\\backend\\src\\index.ts'
    ],
    memoryUsage: {
      rss: 131997696,
      heapTotal: 61108224,
      heapUsed: 32248960,
      external: 4931610,
      arrayBuffers: 161373
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 1695533.328 },
  trace: [
    {
      column: 16,
      file: 'node:net',
      function: 'Server.setupListenHandle [as _listen2]',
      line: 1907,
      method: 'setupListenHandle [as _listen2]',
      native: false
    },
    {
      column: 12,
      file: 'node:net',
      function: 'listenInCluster',
      line: 1964,
      method: null,
      native: false
    },
    {
      column: 7,
      file: 'node:net',
      function: null,
      line: 2170,
      method: null,
      native: false
    },
    {
      column: 21,
      file: 'node:internal/process/task_queues',
      function: 'processTicksAndRejections',
      line: 90,
      method: null,
      native: false
    }
  ],
  timestamp: '2025-07-28 14:32:01'
}
