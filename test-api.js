const axios = require('axios');

async function testAPI() {
  try {
    console.log('Testing backend API connection...');
    
    // Test health endpoint (ignore 503 status, just check if server responds)
    console.log('1. Testing health endpoint...');
    try {
      const healthResponse = await axios.get('http://localhost:5000/health');
      console.log('✅ Health check successful:', healthResponse.data);
    } catch (error) {
      if (error.response && error.response.status === 503) {
        console.log('⚠️ Health check returned 503 (storage unavailable), but server is responding:', error.response.data);
      } else {
        throw error;
      }
    }
    
    // Test registration endpoint
    console.log('\n2. Testing registration endpoint...');
    const email = `test${Date.now()}@example.com`;
    const registrationData = {
      email: email,
      password: 'Test123!',
      name: 'Test User'
    };
    
    const registerResponse = await axios.post('http://localhost:5000/api/auth/register', registrationData, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });
    
    console.log('✅ Registration successful!');
    console.log('Status:', registerResponse.status);
    console.log('User created:', registerResponse.data.data.user.email);
    
    // Test login endpoint
    console.log('\n3. Testing login endpoint...');
    const loginResponse = await axios.post('http://localhost:5000/api/auth/login', {
      email: email,
      password: 'Test123!'
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Login successful!');
    console.log('Token received:', !!loginResponse.data.data.token);
    
    console.log('\n🎉 All API tests passed! Backend is working correctly.');
    
  } catch (error) {
    console.error('❌ API test failed:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error('Error:', error.message);
    }
  }
}

testAPI();
