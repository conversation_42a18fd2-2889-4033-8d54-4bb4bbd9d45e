import { PrismaClient } from '@piknowkyo/database/client';
import { logger } from './logger';

export class DatabaseHandler {
  static async connect() {
    try {
      const prisma = new PrismaClient();
      await prisma.$connect();
      logger.info('Database connected successfully');
    } catch (error) {
      logger.error('Failed to connect to database:', error);
      throw error;
    }
  }

  static async disconnect() {
    try {
      const prisma = new PrismaClient();
      await prisma.$disconnect();
      logger.info('Database disconnected successfully');
    } catch (error) {
      logger.error('Failed to disconnect from database:', error);
      throw error;
    }
  }

  static async healthCheck() {
    try {
      const prisma = new PrismaClient();
      await prisma.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      logger.error('Database health check failed:', error);
      return false;
    }
  }
}

export default DatabaseHandler;
