# Piknowkyo Generator

A professional multilingual video content generation system that automatically creates engaging short-form videos in **French**, **English**, and **Spanish**. The system uses AI to research trends, generate scripts, create visuals, and produce complete videos with localized content for each target language.

## 🌟 Key Features

### Multi-Language Video Support
- **Native support for 3 languages**: French, English, and Spanish
- **Automatic translation and localization**: Content is culturally adapted for each language
- **Language-specific voice synthesis**: Native-sounding text-to-speech for each language
- **Localized content creation**: Scripts and visuals adapted to cultural contexts

### Local-First Architecture
- **No remote database dependencies**: Uses SQLite for local data storage
- **Self-contained system**: All processing happens locally
- **Privacy-focused**: Your content and data stay on your machine
- **Offline capable**: Core functionality works without internet (except for AI API calls)

### Professional Architecture
- **Monorepo structure**: Clean separation of concerns with shared libraries
- **Modular design**: Easy to extend and maintain
- **Type-safe**: Full TypeScript implementation
- **Production-ready**: Comprehensive error handling and logging

## 🏗️ Project Architecture

```
piknowkyo-generator/
├── apps/                               # Main applications
│   ├── backend/                        # Express.js API server
│   ├── frontend/                       # Next.js dashboard
│   └── video-engine/                   # Video processing engine
├── packages/                           # Shared packages
│   ├── shared/                         # Common types and utilities
│   └── database/                       # Database client and handlers
├── database/                           # SQLite database and schema
├── storage/                            # Local file storage
│   ├── videos/french/                  # French language videos
│   ├── videos/english/                 # English language videos
│   └── videos/spanish/                 # Spanish language videos
└── docs/                               # Documentation
```
## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm/yarn
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/piknowkyo-generator.git
   cd piknowkyo-generator
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys and configuration
   ```

5. **Initialize the database**
   ```bash
   # Generate Prisma client
   npm run db:generate --workspace=apps/backend

   # Run database migrations
   npm run db:migrate --workspace=apps/backend

   # Optional: Seed the database with sample data
   npm run db:seed --workspace=apps/backend
   ```

6. **Start the development servers**
   ```bash
   # Start both backend and frontend in development mode
   npm run dev

   # Or start them individually:
   npm run dev:backend    # Backend API server
   npm run dev:frontend   # Frontend dashboard
   ```

### Access the Application

- **Frontend Dashboard**: `http://localhost:3000` (Next.js)
- **Backend API**: `http://localhost:5000` (Express.js)
- **Health Check**: `http://localhost:5000/health`
- **API Documentation**: `http://localhost:5000/api/docs` (if available)

### Production Deployment

For production deployment instructions, see [PRODUCTION.md](./PRODUCTION.md).

## 🌍 Multi-Language Video System

### How It Works

The system creates videos in three languages with full localization:

1. **Content Generation**: AI generates ideas and scripts in the primary language
2. **Translation & Localization**: Content is translated and culturally adapted for each target language
3. **Voice Synthesis**: Native-sounding speech is generated for each language
4. **Video Assembly**: Videos are compiled with language-specific assets and metadata

### Supported Languages

| Language | Code | Voice Selection | Cultural Adaptation |
|----------|------|----------------|-------------------|
| **French** | `fr-FR` | User-configurable from available voices | French expressions and cultural references |
| **English** | `en-US` | User-configurable from available voices | American English idioms and culture |
| **Spanish** | `es-ES` | User-configurable from available voices | Spanish expressions and cultural context |

### Dynamic Configuration System

All settings are user-configurable through the dashboard:

```typescript
// Example of user-configurable settings (stored in database)
interface UserSettings {
  // AI Provider Settings
  aiProviders: {
    scriptGeneration: {
      provider: 'google' | 'openrouter' | 'groq' | 'mistral' | string;
      model: string; // Fetched dynamically from provider API
      temperature: number;
      maxTokens: number;
    };
    translation: {
      provider: 'google' | 'openrouter' | 'groq' | 'mistral' | string;
      model: string;
      temperature: number;
    };
  };

  // Voice Settings per Language
  voiceSettings: {
    FRENCH: {
      provider: 'azure' | 'elevenlabs' | 'google' | string;
      voiceId: string; // Selected from available voices
      speed: number;
      pitch: number;
      volume: number;
    };
    ENGLISH: {
      provider: 'azure' | 'elevenlabs' | 'google' | string;
      voiceId: string;
      speed: number;
      pitch: number;
      volume: number;
    };
    SPANISH: {
      provider: 'azure' | 'elevenlabs' | 'google' | string;
      voiceId: string;
      speed: number;
      pitch: number;
      volume: number;
    };
  };

  // Language-specific AI Model Settings
  languageModels: {
    FRENCH: {
      translationModel: string;
      culturalAdaptationModel: string;
    };
    ENGLISH: {
      scriptGenerationModel: string;
      metadataModel: string;
    };
    SPANISH: {
      translationModel: string;
      culturalAdaptationModel: string;
    };
  };
}
```

## 💾 Local Database Architecture

### Why Local-Only?

- **Privacy**: Your content never leaves your machine
- **Performance**: No network latency for database operations
- **Reliability**: No dependency on external database services
- **Cost**: No ongoing database hosting costs
- **Simplicity**: Easy setup and maintenance

### Database Schema

The system uses SQLite with Prisma ORM for type-safe database operations:

```prisma
model VideoProject {
  id              String   @id @default(cuid())
  title           String
  description     String?
  primaryLanguage Language @default(ENGLISH)
  status          VideoProjectStatus @default(IDEA_GENERATED)

  // Multilingual support
  multilingualVideos MultilingualVideo[]

  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
}

model MultilingualVideo {
  id            String   @id @default(cuid())
  language      Language
  title         String
  description   String?
  script        String?
  audioUrl      String?
  videoUrl      String?
  status        String   @default("SCRIPT_CREATED")

  videoProject  VideoProject @relation(fields: [videoProjectId], references: [id])
  videoProjectId String

  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
}

enum Language {
  FRENCH
  ENGLISH
  SPANISH
}
```

## 🛠️ Development

### Project Structure

```
apps/
├── backend/                    # Express.js API server
│   ├── src/
│   │   ├── controllers/        # HTTP route controllers
│   │   ├── services/           # Business logic
│   │   │   ├── ai/            # AI integration services
│   │   │   ├── multilingual/  # Multi-language processing
│   │   │   └── video/         # Video generation services
│   │   ├── routes/            # API route definitions
│   │   └── middleware/        # Express middleware
│
├── frontend/                   # Next.js dashboard
│   ├── src/app/
│   │   ├── (auth)/            # Authentication pages
│   │   ├── (dashboard)/       # Main application pages
│   │   │   ├── videos/        # Video management
│   │   │   ├── multilingual/  # Language management
│   │   │   └── settings/      # Configuration
│   │   └── api/               # API routes
│
└── video-engine/              # Video processing
    ├── src/
    │   ├── processors/        # Video processing modules
    │   ├── templates/         # Video templates
    └── output/                # Generated videos
```

### Available Scripts

```bash
# Development
npm run dev              # Start all services in development mode
npm run dev:backend      # Start only the backend API
npm run dev:frontend     # Start only the frontend dashboard
npm run dev:engine       # Start only the video engine

# Database
npm run db:setup         # Initialize database and run migrations
npm run db:migrate       # Run database migrations
npm run db:seed          # Seed database with sample data
npm run db:studio        # Open Prisma Studio

# Building
npm run build            # Build all applications
npm run build:backend    # Build backend only
npm run build:frontend   # Build frontend only

# Testing
npm run test             # Run all tests
npm run test:backend     # Test backend only
npm run test:frontend    # Test frontend only

# Linting & Formatting
npm run lint             # Lint all code
npm run format           # Format all code with Prettier
```

### Environment Variables

Copy `.env.example` to `.env` and configure the following:

```bash
# Database
DATABASE_URL="file:./database/dev.db"

# Authentication
JWT_SECRET="your_jwt_secret_key"

# AI Services (Multiple keys per provider for fallback)
GOOGLE_AI_API_KEY_1="your_primary_google_ai_api_key"
GOOGLE_AI_API_KEY_2="your_secondary_google_ai_api_key"
OPENROUTER_API_KEY_1="your_primary_openrouter_api_key"
OPENROUTER_API_KEY_2="your_secondary_openrouter_api_key"
GROQ_API_KEY_1="your_primary_groq_api_key"
GROQ_API_KEY_2="your_secondary_groq_api_key"
MISTRAL_API_KEY_1="your_primary_mistral_api_key"
MISTRAL_API_KEY_2="your_secondary_mistral_api_key"

# Text-to-Speech (Multiple providers and keys)
AZURE_SPEECH_KEY_1="your_primary_azure_speech_key"
AZURE_SPEECH_KEY_2="your_secondary_azure_speech_key"
AZURE_SPEECH_REGION="your_azure_region"
ELEVENLABS_API_KEY_1="your_primary_elevenlabs_api_key"
ELEVENLABS_API_KEY_2="your_secondary_elevenlabs_api_key"
GOOGLE_TTS_API_KEY_1="your_primary_google_tts_key"

# Multi-language Settings (User-configurable defaults)
DEFAULT_LANGUAGE="ENGLISH"
SUPPORTED_LANGUAGES="FRENCH,ENGLISH,SPANISH"

# Storage
CONTENT_OUTPUT_DIR="./storage"
TEMP_DIR="./storage/temp"
```

## 📚 API Documentation

### Video Projects

#### Create a new video project
```http
POST /api/video-projects
Content-Type: application/json
Authorization: Bearer <token>

{
  "title": "My Video Title",
  "description": "Video description",
  "primaryLanguage": "ENGLISH",
  "createMultilingual": true
}
```

#### Get multilingual versions
```http
GET /api/video-projects/{id}/multilingual
Authorization: Bearer <token>
```

### Multi-language Management

#### Create multilingual versions
```http
POST /api/video-projects/{id}/multilingual
Content-Type: application/json
Authorization: Bearer <token>

{
  "targetLanguages": ["FRENCH", "SPANISH"],
  "customPrompts": {
    "FRENCH": "Custom French adaptation prompt",
    "SPANISH": "Custom Spanish adaptation prompt"
  }
}
```

#### Get supported languages
```http
GET /api/multilingual/languages
Authorization: Bearer <token>
```

### AI Provider Management

#### Get available AI providers and models
```http
GET /api/ai-providers
Authorization: Bearer <token>

Response:
{
  "providers": [
    {
      "id": "google",
      "name": "Google AI",
      "models": ["gemini-1.5-pro", "gemini-1.5-flash", "gemini-pro"],
      "isActive": true
    },
    {
      "id": "openrouter",
      "name": "OpenRouter",
      "models": ["anthropic/claude-3-sonnet", "meta-llama/llama-3-70b"],
      "isActive": true
    }
  ]
}
```

#### Update AI provider settings
```http
PUT /api/settings/ai-providers
Content-Type: application/json
Authorization: Bearer <token>

{
  "scriptGeneration": {
    "provider": "google",
    "model": "gemini-1.5-pro",
    "temperature": 0.7,
    "maxTokens": 2048
  },
  "translation": {
    "provider": "mistral",
    "model": "mistral-large",
    "temperature": 0.3
  }
}
```

### Voice Management

#### Get available voices for a language
```http
GET /api/voices?language=FRENCH&provider=azure
Authorization: Bearer <token>

Response:
{
  "voices": [
    {
      "id": "fr-FR-DeniseNeural",
      "name": "Denise (Neural)",
      "language": "fr-FR",
      "gender": "female",
      "style": "cheerful",
      "preview": "/api/voices/preview/fr-FR-DeniseNeural"
    }
  ]
}
```

#### Update voice settings
```http
PUT /api/settings/voices
Content-Type: application/json
Authorization: Bearer <token>

{
  "FRENCH": {
    "provider": "azure",
    "voiceId": "fr-FR-DeniseNeural",
    "speed": 1.0,
    "pitch": 0,
    "volume": 0
  },
  "ENGLISH": {
    "provider": "elevenlabs",
    "voiceId": "21m00Tcm4TlvDq8ikWAM",
    "speed": 1.1,
    "pitch": 0,
    "volume": 0
  }
}
```

#### Preview voice
```http
POST /api/voices/preview
Content-Type: application/json
Authorization: Bearer <token>

{
  "text": "Hello, this is a voice preview",
  "voiceId": "fr-FR-DeniseNeural",
  "provider": "azure",
  "language": "FRENCH"
}
```

## 🔧 Configuration

### Dynamic AI Provider Configuration

All AI models and voices are fetched dynamically from provider APIs and configurable through the dashboard:

#### AI Provider Management
```typescript
// Models are fetched dynamically from each provider
interface AIProviderConfig {
  id: string;
  name: string;
  type: 'google' | 'openrouter' | 'groq' | 'mistral' | 'custom';
  apiKeys: string[]; // Multiple keys for fallback
  baseUrl?: string; // For custom providers
  availableModels: string[]; // Fetched from provider API
  isActive: boolean;
}

// Example API endpoints for fetching models:
// Google AI: GET https://generativelanguage.googleapis.com/v1/models
// OpenRouter: GET https://openrouter.ai/api/v1/models
// Groq: GET https://api.groq.com/openai/v1/models
// Mistral: GET https://api.mistral.ai/v1/models
```

#### Voice Provider Management
```typescript
// Voices are fetched dynamically from TTS providers
interface VoiceProviderConfig {
  id: string;
  name: string;
  type: 'azure' | 'elevenlabs' | 'google' | 'custom';
  apiKeys: string[];
  availableVoices: Voice[]; // Fetched from provider API
  supportedLanguages: string[];
}

interface Voice {
  id: string;
  name: string;
  language: string;
  gender: 'male' | 'female' | 'neutral';
  style?: string;
  preview?: string; // URL to voice sample
}
```

#### User Settings Dashboard

Users can configure everything through the web interface:

1. **AI Provider Settings**
   - Select primary and fallback providers
   - Choose models for each task type
   - Configure temperature, max tokens, etc.

2. **Voice Settings per Language**
   - Select TTS provider
   - Choose voice from available options
   - Adjust speed, pitch, volume
   - Preview voices before selection

3. **Language-Specific AI Models**
   - Different models for translation vs. generation
   - Cultural adaptation settings
   - Custom prompts per language

4. **Custom Provider Support**
   - Add new AI providers with API endpoints
   - Configure custom TTS services
   - Set up provider-specific authentication

## 🎯 Features

### Core Features
- ✅ **Multi-language video generation** (French, English, Spanish)
- ✅ **Local SQLite database** (no remote dependencies)
- ✅ **AI-powered content creation** (scripts, ideas, metadata)
- ✅ **Text-to-speech synthesis** with native voices
- ✅ **Automated video assembly** and rendering
- ✅ **Web dashboard** for content management
- ✅ **RESTful API** for programmatic access

### Advanced Features
- ✅ **Cultural localization** for each language
- ✅ **Batch processing** for multiple videos
- ✅ **Template system** for consistent branding
- ✅ **Performance analytics** and tracking
- ✅ **Comment management** with AI responses
- ✅ **Workflow automation** with scheduling

## 🔒 Security & Privacy

### Data Privacy
- **Local-first approach**: All data stored locally
- **No cloud dependencies**: Database runs on your machine
- **API key security**: Credentials stored in environment variables only
- **Secure authentication**: JWT-based auth with bcrypt password hashing

### Best Practices
- Environment variables for sensitive data
- Input validation and sanitization
- Rate limiting on API endpoints
- Secure file upload handling
- CORS configuration for frontend access

## 🚀 Deployment

### Local Development
```bash
npm run dev
```

### Production Build
```bash
npm run build
npm run start
```

### Docker Deployment
```bash
docker-compose up -d
```

## 🤝 Contributing

### Development Guidelines

1. **Code Style**: Use Prettier and ESLint configurations
2. **TypeScript**: Maintain strict type safety
3. **Testing**: Write tests for new features
4. **Documentation**: Update docs for API changes
5. **Commits**: Use conventional commit messages

### Adding New Languages

To add support for a new language:

1. **Add the language** through the dashboard settings
2. **Configure AI models** for the new language (translation, generation)
3. **Select voices** from available TTS providers
4. **Set cultural adaptation prompts** specific to the language
5. **Test voice synthesis** with sample text

### Adding Custom AI Providers

Users can add unlimited custom AI providers:

1. **Provider Configuration**:
   ```typescript
   interface CustomProvider {
     name: string;
     baseUrl: string;
     apiKeys: string[];
     modelsEndpoint: string;  // e.g., "/v1/models"
     chatEndpoint: string;    // e.g., "/v1/chat/completions"
     headers?: Record<string, string>;
   }
   ```

2. **Through Dashboard**:
   - Go to Settings → AI Providers
   - Click "Add Custom Provider"
   - Enter provider details
   - Test connection and fetch models
   - Configure for specific tasks

3. **Environment Variables**:
   ```bash
   CUSTOM_PROVIDER_1_NAME="Your Provider"
   CUSTOM_PROVIDER_1_BASE_URL="https://api.yourprovider.com"
   CUSTOM_PROVIDER_1_API_KEY_1="your_api_key"
   CUSTOM_PROVIDER_1_MODELS_ENDPOINT="/models"
   CUSTOM_PROVIDER_1_CHAT_ENDPOINT="/chat/completions"
   ```

### Adding Custom TTS Providers

Similarly, users can add custom text-to-speech providers:

1. **TTS Provider Configuration**:
   ```typescript
   interface CustomTTSProvider {
     name: string;
     baseUrl: string;
     apiKeys: string[];
     voicesEndpoint: string;     // e.g., "/voices"
     synthesisEndpoint: string;  // e.g., "/synthesize"
     supportedLanguages: string[];
   }
   ```

2. **Voice Fetching**: The system automatically fetches available voices from the provider's API
3. **Language Support**: Configure which languages the provider supports
4. **Voice Preview**: Test voices before selection

### Project Roadmap

- [ ] **Video Templates**: More customizable video templates
- [ ] **Advanced AI**: Better content generation with latest models
- [ ] **Social Integration**: Direct publishing to social platforms
- [ ] **Analytics Dashboard**: Enhanced performance tracking
- [ ] **Mobile App**: React Native mobile application
- [ ] **Plugin System**: Extensible plugin architecture

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **OpenAI** for GPT models
- **Google** for Gemini AI and Cloud services
- **Azure** for Speech Services
- **Prisma** for database ORM
- **Next.js** for the frontend framework
- **Express.js** for the backend API

---

**Built with ❤️ for content creators who want to reach global audiences**
```
```