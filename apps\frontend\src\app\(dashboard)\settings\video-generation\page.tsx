'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

interface VideoGenerationConfig {
  id?: string;
  // Image Generation Settings
  imageProvider: string;
  imageModel: string;
  imageStyle: 'realistic' | 'artistic' | 'cartoon' | 'cinematic' | 'custom';
  imageResolution: '1024x1024' | '1024x1792' | '1792x1024' | 'custom';
  imageQuality: 'standard' | 'hd' | 'ultra';
  
  // Video Assembly Settings
  transitionType: 'fade' | 'slide' | 'zoom' | 'dissolve' | 'wipe' | 'custom';
  transitionDuration: number;
  animationType: 'none' | 'ken_burns' | 'parallax' | 'zoom_in' | 'zoom_out' | 'custom';
  animationIntensity: 'subtle' | 'moderate' | 'dramatic';
  
  // Audio Settings
  backgroundMusic: boolean;
  musicVolume: number;
  voiceVolume: number;
  audioFadeIn: number;
  audioFadeOut: number;
  
  // Timing Settings
  imageDisplayDuration: number;
  textDisplayDuration: number;
  totalVideoDuration?: number;
  
  // Quality Settings
  videoResolution: '720p' | '1080p' | '4k';
  videoFrameRate: 24 | 30 | 60;
  videoBitrate: 'auto' | 'low' | 'medium' | 'high' | 'ultra';
}

export default function VideoGenerationPage() {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  const [config, setConfig] = useState<VideoGenerationConfig>({
    imageProvider: 'OPENAI',
    imageModel: 'dall-e-3',
    imageStyle: 'realistic',
    imageResolution: '1024x1024',
    imageQuality: 'standard',
    transitionType: 'fade',
    transitionDuration: 1.0,
    animationType: 'ken_burns',
    animationIntensity: 'moderate',
    backgroundMusic: true,
    musicVolume: 30,
    voiceVolume: 80,
    audioFadeIn: 2.0,
    audioFadeOut: 2.0,
    imageDisplayDuration: 4.0,
    textDisplayDuration: 3.0,
    videoResolution: '1080p',
    videoFrameRate: 30,
    videoBitrate: 'medium'
  });
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (user) {
      loadConfig();
    }
  }, [user]);

  const loadConfig = async () => {
    setIsLoadingData(true);
    try {
      const response = await fetch('/api/settings/video-generation');
      if (response.ok) {
        const data = await response.json();
        if (data.data) {
          setConfig(data.data);
        }
      }
    } catch (error) {
      console.error('Error loading video generation config:', error);
    } finally {
      setIsLoadingData(false);
    }
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      const response = await fetch('/api/settings/video-generation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config),
      });

      if (response.ok) {
        alert('Configuration sauvegardée avec succès !');
      } else {
        const error = await response.json();
        alert(`Erreur: ${error.message || 'Échec de la sauvegarde'}`);
      }
    } catch (error) {
      console.error('Error saving config:', error);
      alert('Échec de la sauvegarde. Veuillez réessayer.');
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!user) {
    router.push('/auth/login');
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container-wide py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Configuration Génération Vidéo</h1>
          <p className="mt-2 text-gray-600">
            Configurez tous les aspects de la génération vidéo : images, transitions, animations, audio et qualité
          </p>
        </div>

        {isLoadingData ? (
          <div className="flex justify-center py-12">
            <LoadingSpinner size="lg" />
          </div>
        ) : (
          <div className="space-y-8">
            {/* Image Generation Settings */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Génération d'Images</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Fournisseur d'Images
                  </label>
                  <select
                    className="input-field"
                    value={config.imageProvider}
                    onChange={(e) => setConfig(prev => ({ ...prev, imageProvider: e.target.value }))}
                  >
                    <option value="OPENAI">OpenAI (DALL-E)</option>
                    <option value="MIDJOURNEY">Midjourney</option>
                    <option value="STABLE_DIFFUSION">Stable Diffusion</option>
                    <option value="LEONARDO">Leonardo AI</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Modèle
                  </label>
                  <select
                    className="input-field"
                    value={config.imageModel}
                    onChange={(e) => setConfig(prev => ({ ...prev, imageModel: e.target.value }))}
                  >
                    <option value="dall-e-3">DALL-E 3</option>
                    <option value="dall-e-2">DALL-E 2</option>
                    <option value="midjourney-v6">Midjourney v6</option>
                    <option value="sd-xl">Stable Diffusion XL</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Style d'Image
                  </label>
                  <select
                    className="input-field"
                    value={config.imageStyle}
                    onChange={(e) => setConfig(prev => ({ ...prev, imageStyle: e.target.value as any }))}
                  >
                    <option value="realistic">Réaliste</option>
                    <option value="artistic">Artistique</option>
                    <option value="cartoon">Cartoon</option>
                    <option value="cinematic">Cinématique</option>
                    <option value="custom">Personnalisé</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Résolution
                  </label>
                  <select
                    className="input-field"
                    value={config.imageResolution}
                    onChange={(e) => setConfig(prev => ({ ...prev, imageResolution: e.target.value as any }))}
                  >
                    <option value="1024x1024">1024x1024 (Carré)</option>
                    <option value="1024x1792">1024x1792 (Portrait)</option>
                    <option value="1792x1024">1792x1024 (Paysage)</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Qualité
                  </label>
                  <select
                    className="input-field"
                    value={config.imageQuality}
                    onChange={(e) => setConfig(prev => ({ ...prev, imageQuality: e.target.value as any }))}
                  >
                    <option value="standard">Standard</option>
                    <option value="hd">HD</option>
                    <option value="ultra">Ultra</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Video Assembly Settings */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Assemblage Vidéo</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Type de Transition
                  </label>
                  <select
                    className="input-field"
                    value={config.transitionType}
                    onChange={(e) => setConfig(prev => ({ ...prev, transitionType: e.target.value as any }))}
                  >
                    <option value="fade">Fondu</option>
                    <option value="slide">Glissement</option>
                    <option value="zoom">Zoom</option>
                    <option value="dissolve">Dissolution</option>
                    <option value="wipe">Balayage</option>
                    <option value="custom">Personnalisé</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Durée Transition (s)
                  </label>
                  <input
                    type="number"
                    className="input-field"
                    value={config.transitionDuration}
                    onChange={(e) => setConfig(prev => ({ ...prev, transitionDuration: parseFloat(e.target.value) }))}
                    min="0.1"
                    max="5"
                    step="0.1"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Type d'Animation
                  </label>
                  <select
                    className="input-field"
                    value={config.animationType}
                    onChange={(e) => setConfig(prev => ({ ...prev, animationType: e.target.value as any }))}
                  >
                    <option value="none">Aucune</option>
                    <option value="ken_burns">Ken Burns</option>
                    <option value="parallax">Parallax</option>
                    <option value="zoom_in">Zoom In</option>
                    <option value="zoom_out">Zoom Out</option>
                    <option value="custom">Personnalisé</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Intensité Animation
                  </label>
                  <select
                    className="input-field"
                    value={config.animationIntensity}
                    onChange={(e) => setConfig(prev => ({ ...prev, animationIntensity: e.target.value as any }))}
                  >
                    <option value="subtle">Subtile</option>
                    <option value="moderate">Modérée</option>
                    <option value="dramatic">Dramatique</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Audio Settings */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Paramètres Audio</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="backgroundMusic"
                    className="mr-3"
                    checked={config.backgroundMusic}
                    onChange={(e) => setConfig(prev => ({ ...prev, backgroundMusic: e.target.checked }))}
                  />
                  <label htmlFor="backgroundMusic" className="text-sm font-medium text-gray-700">
                    Musique de fond
                  </label>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Volume Musique ({config.musicVolume}%)
                  </label>
                  <input
                    type="range"
                    className="w-full"
                    value={config.musicVolume}
                    onChange={(e) => setConfig(prev => ({ ...prev, musicVolume: parseInt(e.target.value) }))}
                    min="0"
                    max="100"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Volume Voix ({config.voiceVolume}%)
                  </label>
                  <input
                    type="range"
                    className="w-full"
                    value={config.voiceVolume}
                    onChange={(e) => setConfig(prev => ({ ...prev, voiceVolume: parseInt(e.target.value) }))}
                    min="0"
                    max="100"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Fondu Entrant (s)
                  </label>
                  <input
                    type="number"
                    className="input-field"
                    value={config.audioFadeIn}
                    onChange={(e) => setConfig(prev => ({ ...prev, audioFadeIn: parseFloat(e.target.value) }))}
                    min="0"
                    max="10"
                    step="0.5"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Fondu Sortant (s)
                  </label>
                  <input
                    type="number"
                    className="input-field"
                    value={config.audioFadeOut}
                    onChange={(e) => setConfig(prev => ({ ...prev, audioFadeOut: parseFloat(e.target.value) }))}
                    min="0"
                    max="10"
                    step="0.5"
                  />
                </div>
              </div>
            </div>

            {/* Save Button */}
            <div className="flex justify-end">
              <button
                onClick={handleSave}
                disabled={isSaving}
                className="btn-primary"
              >
                {isSaving ? <LoadingSpinner size="sm" /> : 'Sauvegarder Configuration'}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
