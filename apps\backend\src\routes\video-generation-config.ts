import { Router, Response } from 'express';
import { body } from 'express-validator';
import { validate } from '../middleware/validation';
import { asyncHandler } from '../utils/async-handler';
import { authMiddleware, AuthenticatedRequest } from '../middleware/auth';
import { prisma } from '@piknowkyo/database/client';
import { createError } from '../middleware/error-handler';
import { ERROR_CODES } from '@piknowkyo/shared/constants';

const router = Router();

// Validation rules
const videoGenerationConfigValidation = [
  body('imageProvider')
    .isIn(['OPENAI', 'MIDJOURNEY', 'STABLE_DIFFUSION', 'LEONARDO'])
    .withMessage('Invalid image provider'),
  body('imageModel')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Image model must be between 1 and 100 characters'),
  body('imageStyle')
    .isIn(['realistic', 'artistic', 'cartoon', 'cinematic', 'custom'])
    .withMessage('Invalid image style'),
  body('imageResolution')
    .isIn(['1024x1024', '1024x1792', '1792x1024', 'custom'])
    .withMessage('Invalid image resolution'),
  body('imageQuality')
    .isIn(['standard', 'hd', 'ultra'])
    .withMessage('Invalid image quality'),
  body('transitionType')
    .isIn(['fade', 'slide', 'zoom', 'dissolve', 'wipe', 'custom'])
    .withMessage('Invalid transition type'),
  body('transitionDuration')
    .isFloat({ min: 0.1, max: 10 })
    .withMessage('Transition duration must be between 0.1 and 10 seconds'),
  body('animationType')
    .isIn(['none', 'ken_burns', 'parallax', 'zoom_in', 'zoom_out', 'custom'])
    .withMessage('Invalid animation type'),
  body('animationIntensity')
    .isIn(['subtle', 'moderate', 'dramatic'])
    .withMessage('Invalid animation intensity'),
  body('backgroundMusic')
    .isBoolean()
    .withMessage('Background music must be a boolean'),
  body('musicVolume')
    .isInt({ min: 0, max: 100 })
    .withMessage('Music volume must be between 0 and 100'),
  body('voiceVolume')
    .isInt({ min: 0, max: 100 })
    .withMessage('Voice volume must be between 0 and 100'),
  body('audioFadeIn')
    .isFloat({ min: 0, max: 30 })
    .withMessage('Audio fade in must be between 0 and 30 seconds'),
  body('audioFadeOut')
    .isFloat({ min: 0, max: 30 })
    .withMessage('Audio fade out must be between 0 and 30 seconds'),
  body('imageDisplayDuration')
    .isFloat({ min: 0.5, max: 60 })
    .withMessage('Image display duration must be between 0.5 and 60 seconds'),
  body('textDisplayDuration')
    .isFloat({ min: 0.5, max: 60 })
    .withMessage('Text display duration must be between 0.5 and 60 seconds'),
  body('totalVideoDuration')
    .optional()
    .isInt({ min: 10, max: 3600 })
    .withMessage('Total video duration must be between 10 and 3600 seconds'),
  body('videoResolution')
    .isIn(['720p', '1080p', '4k'])
    .withMessage('Invalid video resolution'),
  body('videoFrameRate')
    .isIn([24, 30, 60])
    .withMessage('Invalid video frame rate'),
  body('videoBitrate')
    .isIn(['auto', 'low', 'medium', 'high', 'ultra'])
    .withMessage('Invalid video bitrate')
];

// GET /api/settings/video-generation - Get video generation configuration
router.get('/', authMiddleware, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;

  const config = await prisma.videoGenerationConfig.findUnique({
    where: { userId }
  });

  if (!config) {
    // Return default configuration
    const defaultConfig = {
      imageProvider: 'OPENAI',
      imageModel: 'dall-e-3',
      imageStyle: 'realistic',
      imageResolution: '1024x1024',
      imageQuality: 'standard',
      transitionType: 'fade',
      transitionDuration: 1.0,
      animationType: 'ken_burns',
      animationIntensity: 'moderate',
      backgroundMusic: true,
      musicVolume: 30,
      voiceVolume: 80,
      audioFadeIn: 2.0,
      audioFadeOut: 2.0,
      imageDisplayDuration: 4.0,
      textDisplayDuration: 3.0,
      videoResolution: '1080p',
      videoFrameRate: 30,
      videoBitrate: 'medium'
    };

    res.json({
      success: true,
      data: defaultConfig,
      timestamp: new Date().toISOString()
    });
    return;
  }

  res.json({
    success: true,
    data: {
      id: config.id,
      imageProvider: config.imageProvider,
      imageModel: config.imageModel,
      imageStyle: config.imageStyle,
      imageResolution: config.imageResolution,
      imageQuality: config.imageQuality,
      transitionType: config.transitionType,
      transitionDuration: config.transitionDuration,
      animationType: config.animationType,
      animationIntensity: config.animationIntensity,
      backgroundMusic: config.backgroundMusic,
      musicVolume: config.musicVolume,
      voiceVolume: config.voiceVolume,
      audioFadeIn: config.audioFadeIn,
      audioFadeOut: config.audioFadeOut,
      imageDisplayDuration: config.imageDisplayDuration,
      textDisplayDuration: config.textDisplayDuration,
      totalVideoDuration: config.totalVideoDuration,
      videoResolution: config.videoResolution,
      videoFrameRate: config.videoFrameRate,
      videoBitrate: config.videoBitrate
    },
    timestamp: new Date().toISOString()
  });
}));

// POST /api/settings/video-generation - Create or update video generation configuration
router.post('/', authMiddleware, validate(videoGenerationConfigValidation), asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;
  const {
    imageProvider,
    imageModel,
    imageStyle,
    imageResolution,
    imageQuality,
    transitionType,
    transitionDuration,
    animationType,
    animationIntensity,
    backgroundMusic,
    musicVolume,
    voiceVolume,
    audioFadeIn,
    audioFadeOut,
    imageDisplayDuration,
    textDisplayDuration,
    totalVideoDuration,
    videoResolution,
    videoFrameRate,
    videoBitrate
  } = req.body;

  const config = await prisma.videoGenerationConfig.upsert({
    where: { userId },
    create: {
      userId,
      imageProvider,
      imageModel,
      imageStyle,
      imageResolution,
      imageQuality,
      transitionType,
      transitionDuration,
      animationType,
      animationIntensity,
      backgroundMusic,
      musicVolume,
      voiceVolume,
      audioFadeIn,
      audioFadeOut,
      imageDisplayDuration,
      textDisplayDuration,
      totalVideoDuration,
      videoResolution,
      videoFrameRate,
      videoBitrate
    },
    update: {
      imageProvider,
      imageModel,
      imageStyle,
      imageResolution,
      imageQuality,
      transitionType,
      transitionDuration,
      animationType,
      animationIntensity,
      backgroundMusic,
      musicVolume,
      voiceVolume,
      audioFadeIn,
      audioFadeOut,
      imageDisplayDuration,
      textDisplayDuration,
      totalVideoDuration,
      videoResolution,
      videoFrameRate,
      videoBitrate,
      updatedAt: new Date()
    }
  });

  res.json({
    success: true,
    data: {
      id: config.id,
      imageProvider: config.imageProvider,
      imageModel: config.imageModel,
      imageStyle: config.imageStyle,
      imageResolution: config.imageResolution,
      imageQuality: config.imageQuality,
      transitionType: config.transitionType,
      transitionDuration: config.transitionDuration,
      animationType: config.animationType,
      animationIntensity: config.animationIntensity,
      backgroundMusic: config.backgroundMusic,
      musicVolume: config.musicVolume,
      voiceVolume: config.voiceVolume,
      audioFadeIn: config.audioFadeIn,
      audioFadeOut: config.audioFadeOut,
      imageDisplayDuration: config.imageDisplayDuration,
      textDisplayDuration: config.textDisplayDuration,
      totalVideoDuration: config.totalVideoDuration,
      videoResolution: config.videoResolution,
      videoFrameRate: config.videoFrameRate,
      videoBitrate: config.videoBitrate
    },
    message: 'Video generation configuration saved successfully',
    timestamp: new Date().toISOString()
  });
}));

// DELETE /api/settings/video-generation - Reset to default configuration
router.delete('/', authMiddleware, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;

  await prisma.videoGenerationConfig.deleteMany({
    where: { userId }
  });

  res.json({
    success: true,
    message: 'Video generation configuration reset to defaults',
    timestamp: new Date().toISOString()
  });
}));

export default router;
