import { Request, Response, NextFunction } from 'express';
import { ValidationError } from 'express-validator';
import { <PERSON>risma } from '@piknowkyo/database';
import { logger } from '../config/logger';
import { ERROR_CODES } from '@piknowkyo/shared/constants';

export interface AppError extends Error {
  statusCode?: number;
  code?: string;
  isOperational?: boolean;
  meta?: any;
}

// Type guard to check if error is an AppError
const isAppError = (error: Error | AppError): error is AppError => {
  return 'code' in error || 'statusCode' in error || 'isOperational' in error;
};

export class CustomError extends Error implements AppError {
  public statusCode: number;
  public code: string;
  public isOperational: boolean;

  constructor(message: string, statusCode: number = 500, code: string = ERROR_CODES.INTERNAL_ERROR as string) {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

export const createError = (
  message: string,
  statusCode: number = 500,
  code: string = ERROR_CODES.INTERNAL_ERROR as string
): CustomError => {
  return new CustomError(message, statusCode, code);
};

export const errorHandler = (
  error: AppError | Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  let statusCode = 500;
  let code: string = ERROR_CODES.INTERNAL_ERROR as string;
  let message = 'Internal server error';
  let details: any = undefined;

  // Log the error
  logger.error('Error occurred:', {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  // Handle different error types
  if (error instanceof CustomError) {
    statusCode = error.statusCode;
    code = error.code;
    message = error.message;
  } else if (isAppError(error) && error.code) {
    statusCode = error.statusCode || 500;
    code = error.code;
    message = error.message;
    if (error.meta) {
      details = error.meta;
    }
  } else if (error instanceof Prisma.PrismaClientKnownRequestError) {
    // Handle Prisma errors
    switch (error.code) {
      case 'P2002':
        statusCode = 409;
        code = ERROR_CODES.ALREADY_EXISTS;
        message = 'Resource already exists';
        details = { field: error.meta?.target };
        break;
      case 'P2025':
        statusCode = 404;
        code = ERROR_CODES.NOT_FOUND;
        message = 'Resource not found';
        break;
      case 'P2003':
        statusCode = 400;
        code = ERROR_CODES.VALIDATION_ERROR;
        message = 'Foreign key constraint failed';
        break;
      default:
        statusCode = 500;
        code = ERROR_CODES.INTERNAL_ERROR;
        message = 'Database error';
    }
  } else if (error instanceof Prisma.PrismaClientValidationError) {
    statusCode = 400;
    code = ERROR_CODES.VALIDATION_ERROR;
    message = 'Invalid data provided';
  } else if (error.name === 'ValidationError') {
    // Handle express-validator errors
    statusCode = 400;
    code = ERROR_CODES.VALIDATION_ERROR;
    message = 'Validation failed';
    details = error.message;
  } else if (error.name === 'JsonWebTokenError') {
    statusCode = 401;
    code = ERROR_CODES.UNAUTHORIZED;
    message = 'Invalid token';
  } else if (error.name === 'TokenExpiredError') {
    statusCode = 401;
    code = ERROR_CODES.TOKEN_EXPIRED;
    message = 'Token expired';
  } else if (error.name === 'MulterError') {
    statusCode = 400;
    code = ERROR_CODES.VALIDATION_ERROR;
    message = 'File upload error';
    details = error.message;
  } else if (error.message.includes('ENOENT')) {
    statusCode = 404;
    code = ERROR_CODES.NOT_FOUND;
    message = 'File not found';
  } else if (error.message.includes('EACCES')) {
    statusCode = 403;
    code = ERROR_CODES.FORBIDDEN;
    message = 'Permission denied';
  }

  // Don't expose internal errors in production
  if (process.env.NODE_ENV === 'production' && statusCode === 500) {
    message = 'Internal server error';
    details = undefined;
  }

  const errorResponse = {
    success: false,
    error: code,
    message,
    ...(details && { details }),
    timestamp: new Date().toISOString(),
    ...(process.env.NODE_ENV === 'development' && {
      stack: error.stack,
      path: req.path,
      method: req.method
    })
  };

  res.status(statusCode).json(errorResponse);
};

export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

export const notFound = (req: Request, res: Response, next: NextFunction) => {
  const error = createError(
    `Route ${req.originalUrl} not found`,
    404,
    ERROR_CODES.NOT_FOUND
  );
  next(error);
};
