import type { Metada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import { Providers } from '@/components/providers';
import { Toaster } from 'react-hot-toast';
import '@/styles/globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Piknowkyo Generator - Multilingual Video Content Studio',
  description: 'Create multilingual video content automatically with AI-powered translation and voice generation for French, English, and Spanish.',
  keywords: ['video generation', 'multilingual', 'AI', 'content creation', 'French', 'English', 'Spanish'],
  authors: [{ name: 'Piknowkyo Team' }],
  robots: 'index, follow',
  openGraph: {
    title: 'Piknowkyo Generator',
    description: 'Create multilingual video content automatically',
    type: 'website',
    locale: 'en_US',
    alternateLocale: ['fr_FR', 'es_ES'],
  },
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className="h-full">
      <body className={`${inter.className} h-full bg-gray-50 antialiased`}>
        <Providers>
          {children}
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#363636',
                color: '#fff',
              },
              success: {
                style: {
                  background: '#10b981',
                },
              },
              error: {
                style: {
                  background: '#ef4444',
                },
              },
            }}
          />
        </Providers>
      </body>
    </html>
  );
}
