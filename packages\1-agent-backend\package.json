{"name": "@piknowkyo/1-agent-backend", "version": "1.0.0", "description": "Agent backend core utilities for Piknowkyo Generator", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit"}, "dependencies": {"@piknowkyo/database": "file:../database", "@piknowkyo/shared": "file:../shared", "winston": "^3.10.0"}, "devDependencies": {"typescript": "^5.0.0", "@types/node": "^20.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./core/*": {"types": "./dist/core/*.d.ts", "default": "./dist/core/*.js"}}}