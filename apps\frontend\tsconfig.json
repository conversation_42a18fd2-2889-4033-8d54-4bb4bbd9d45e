{"extends": "../../tsconfig.json", "compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/lib/*": ["./src/lib/*"], "@/hooks/*": ["./src/hooks/*"], "@/types/*": ["./src/types/*"], "@/utils/*": ["./src/utils/*"], "@/styles/*": ["./src/styles/*"], "@shared/*": ["../../packages/shared/src/*"], "@piknowkyo/shared": ["../../packages/shared/dist/index.d.ts"], "@piknowkyo/shared/*": ["../../packages/shared/dist/*"], "@piknowkyo/database": ["../../packages/database/dist/index.d.ts"], "@piknowkyo/database/*": ["../../packages/database/dist/*"]}}, "include": ["next-env.d.ts", "src/types/global.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}