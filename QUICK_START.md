# Guide de démarrage rapide - Piknowkyo Generator

## Étapes pour exécuter l'application

### 1. Installer les dépendances
```bash
npm install
```

### 2. Construire les packages partagés
```bash
npm run build --workspace=packages/shared
npm run build --workspace=packages/database
npm run build --workspace=packages/1-agent-backend
```

### 3. Configurer l'environnement
Le fichier `.env` existe déjà avec la configuration par défaut. Vous pouvez l'éditer si nécessaire.

**Important** : Le backend fonctionne sur le port 5000 et le frontend a été configuré pour s'y connecter automatiquement.

### 4. Initialiser la base de données
```bash
# Générer le client Prisma
npm run db:generate --workspace=apps/backend

# Exécuter les migrations (si nécessaire)
npm run db:migrate --workspace=apps/backend
```

### 5. Démarrer l'application

#### Option A : Démarrer backend et frontend ensemble
```bash
npm run dev
```

#### Option B : Démarrer individuellement
```bash
# Backend seulement
npm run dev:backend

# Frontend seulement (dans un autre terminal)
npm run dev:frontend
```

## Accès aux services

Une fois l'application démarrée :

- **Backend API** : http://localhost:5000
- **Health Check** : http://localhost:5000/health
- **Frontend Dashboard** : http://localhost:3000 (si démarré)

## Vérification que tout fonctionne

Testez le health check :
```bash
curl http://localhost:5000/health
```

Vous devriez voir une réponse JSON avec le statut de l'application.

## Problèmes courants

1. **Erreurs TypeScript** : Assurez-vous que tous les packages sont construits
2. **Base de données non connectée** : Vérifiez que le fichier `.env` contient `DATABASE_URL="file:./database/dev.db"`
3. **Port déjà utilisé** : Changez le port dans le fichier `.env` (variable `PORT`)

## Structure des packages

- `packages/shared` : Types et constantes partagés
- `packages/database` : Client Prisma et utilitaires de base de données  
- `packages/1-agent-backend` : Utilitaires backend pour l'agent
- `apps/backend` : Serveur API Express.js
- `apps/frontend` : Interface utilisateur Next.js
