export declare const ERROR_CODES: {
    readonly INTERNAL_ERROR: "INTERNAL_ERROR";
    readonly VALIDATION_ERROR: "VALIDATION_ERROR";
    readonly NOT_FOUND: "NOT_FOUND";
    readonly ALREADY_EXISTS: "ALREADY_EXISTS";
    readonly FORBIDDEN: "FORBIDDEN";
    readonly UNAUTHORIZED: "UNAUTHORIZED";
    readonly INVALID_CREDENTIALS: "INVALID_CREDENTIALS";
    readonly TOKEN_EXPIRED: "TOKEN_EXPIRED";
    readonly RATE_LIMIT_EXCEEDED: "RATE_LIMIT_EXCEEDED";
    readonly API_KEY_INVALID: "API_KEY_INVALID";
    readonly API_QUOTA_EXCEEDED: "API_QUOTA_EXCEEDED";
    readonly REQUEST_TOO_LARGE: "REQUEST_TOO_LARGE";
    readonly MISSING_REQUIRED_FIELD: "MISSING_REQUIRED_FIELD";
    readonly FILE_TOO_LARGE: "FILE_TOO_LARGE";
    readonly INVALID_FILE_TYPE: "INVALID_FILE_TYPE";
    readonly FILE_UPLOAD_ERROR: "FILE_UPLOAD_ERROR";
    readonly VIDEO_PROCESSING_ERROR: "VIDEO_PROCESSING_ERROR";
    readonly AUDIO_PROCESSING_ERROR: "AUDIO_PROCESSING_ERROR";
    readonly AI_PROVIDER_ERROR: "AI_PROVIDER_ERROR";
    readonly MODEL_NOT_AVAILABLE: "MODEL_NOT_AVAILABLE";
    readonly TTS_PROVIDER_ERROR: "TTS_PROVIDER_ERROR";
    readonly VOICE_NOT_AVAILABLE: "VOICE_NOT_AVAILABLE";
};
export declare const AI_PROVIDERS: {
    readonly OPENAI: "OPENAI";
    readonly ANTHROPIC: "ANTHROPIC";
    readonly GOOGLE: "GOOGLE";
    readonly MISTRAL: "MISTRAL";
    readonly COHERE: "COHERE";
};
export declare const TTS_PROVIDERS: {
    readonly OPENAI: "OPENAI";
    readonly ELEVENLABS: "ELEVENLABS";
    readonly GOOGLE: "GOOGLE";
    readonly AZURE: "AZURE";
    readonly AWS: "AWS";
};
export declare const LANGUAGES: {
    readonly ENGLISH: "ENGLISH";
    readonly FRENCH: "FRENCH";
    readonly SPANISH: "SPANISH";
    readonly GERMAN: "GERMAN";
    readonly ITALIAN: "ITALIAN";
    readonly PORTUGUESE: "PORTUGUESE";
    readonly DUTCH: "DUTCH";
    readonly RUSSIAN: "RUSSIAN";
    readonly CHINESE: "CHINESE";
    readonly JAPANESE: "JAPANESE";
    readonly KOREAN: "KOREAN";
    readonly ARABIC: "ARABIC";
};
export declare const VIDEO_PROJECT_STATUS: {
    readonly DRAFT: "DRAFT";
    readonly PROCESSING: "PROCESSING";
    readonly COMPLETED: "COMPLETED";
    readonly FAILED: "FAILED";
    readonly CANCELLED: "CANCELLED";
};
export declare const VOICE_GENDER: {
    readonly MALE: "MALE";
    readonly FEMALE: "FEMALE";
    readonly NEUTRAL: "NEUTRAL";
};
export declare const USER_ROLES: {
    readonly USER: "USER";
    readonly ADMIN: "ADMIN";
    readonly SUPER_ADMIN: "SUPER_ADMIN";
};
export declare const API_LIMITS: {
    readonly MAX_FILE_SIZE: number;
    readonly MAX_VIDEO_DURATION: 600;
    readonly MAX_REQUESTS_PER_MINUTE: 60;
    readonly MAX_REQUESTS_PER_HOUR: 1000;
    readonly MAX_REQUESTS_PER_DAY: 10000;
};
export declare const ALLOWED_FILE_TYPES: {
    readonly IMAGES: readonly ["image/jpeg", "image/png", "image/webp", "image/gif"];
    readonly VIDEOS: readonly ["video/mp4", "video/webm", "video/quicktime"];
    readonly AUDIO: readonly ["audio/mpeg", "audio/wav", "audio/ogg", "audio/mp3"];
};
//# sourceMappingURL=constants.d.ts.map