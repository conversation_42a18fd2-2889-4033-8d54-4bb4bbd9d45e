'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { AITaskType, AITaskConfiguration, AIProviderWithDetails } from '@piknowkyo/shared/types';

interface TaskConfigurationProps {
  taskType: AITaskType;
  config: AITaskConfiguration | null;
  providers: AIProviderWithDetails[];
  onSave: (config: Partial<AITaskConfiguration>) => Promise<void>;
}

const TaskConfiguration: React.FC<TaskConfigurationProps> = ({ taskType, config, providers, onSave }) => {
  const [localConfig, setLocalConfig] = useState<Partial<AITaskConfiguration>>({
    taskType,
    providerId: config?.providerId || '',
    modelName: config?.modelName || '',
    temperature: config?.temperature || 0.7,
    maxTokens: config?.maxTokens || 2048,
    topP: config?.topP || 1.0,
    frequencyPenalty: config?.frequencyPenalty || 0,
    presencePenalty: config?.presencePenalty || 0,
    customPrompt: config?.customPrompt || '',
    fallbackProviderId: config?.fallbackProviderId || '',
    fallbackModelName: config?.fallbackModelName || '',
    isActive: config?.isActive ?? true,
    priority: config?.priority || 1,
    maxRetries: config?.maxRetries || 3,
    retryDelay: config?.retryDelay || 5
  });

  const [selectedProvider, setSelectedProvider] = useState<AIProviderWithDetails | null>(null);
  const [availableModels, setAvailableModels] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (localConfig.providerId) {
      const provider = providers.find(p => p.id === localConfig.providerId);
      setSelectedProvider(provider || null);
      setAvailableModels(provider?.models.map(m => m.name) || []);
    }
  }, [localConfig.providerId, providers]);

  const handleSave = async () => {
    setIsLoading(true);
    try {
      await onSave(localConfig);
    } finally {
      setIsLoading(false);
    }
  };

  const getTaskDisplayName = (task: AITaskType): string => {
    const taskNames: Record<AITaskType, string> = {
      research: 'Recherche Internet',
      content_analysis: 'Analyse de Contenu',
      script_generation: 'Génération de Scripts',
      translation: 'Traduction',
      image_generation: 'Génération d\'Images',
      image_analysis: 'Analyse d\'Images',
      metadata_generation: 'Métadonnées',
      voice_script: 'Script Vocal',
      video_planning: 'Planification Vidéo',
      transition_planning: 'Transitions',
      animation_planning: 'Animations',
      seo_optimization: 'Optimisation SEO',
      hashtag_generation: 'Hashtags',
      thumbnail_text: 'Texte Miniature',
      fallback: 'Fallback'
    };
    return taskNames[task] || task;
  };

  const getTaskDescription = (task: AITaskType): string => {
    const descriptions: Record<AITaskType, string> = {
      research: 'Recherche de tendances et d\'informations sur internet',
      content_analysis: 'Analyse et compréhension du contenu existant',
      script_generation: 'Création de scripts pour les vidéos',
      translation: 'Traduction multilingue du contenu',
      image_generation: 'Création d\'images et de miniatures',
      image_analysis: 'Analyse et description d\'images',
      metadata_generation: 'Génération de titres, descriptions et tags',
      voice_script: 'Optimisation des scripts pour la voix',
      video_planning: 'Planification de la structure vidéo',
      transition_planning: 'Planification des transitions vidéo',
      animation_planning: 'Planification des séquences d\'animation',
      seo_optimization: 'Optimisation du contenu pour le SEO',
      hashtag_generation: 'Génération de hashtags pour les réseaux sociaux',
      thumbnail_text: 'Génération de texte pour les miniatures',
      fallback: 'Configuration de secours en cas d\'échec'
    };
    return descriptions[task] || '';
  };

  const filteredProviders = providers.filter(provider => {
    if (taskType === 'research' && !provider.hasInternetAccess) return false;
    if (taskType === 'image_generation' && !provider.supportedTaskTypes?.includes('image_generation')) return false;
    return provider.isConfigured && provider.isActive;
  });

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-900">{getTaskDisplayName(taskType)}</h3>
        <p className="text-sm text-gray-600 mt-1">{getTaskDescription(taskType)}</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Provider Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Fournisseur Principal
          </label>
          <select
            className="input-field"
            value={localConfig.providerId}
            onChange={(e) => setLocalConfig(prev => ({ ...prev, providerId: e.target.value, modelName: '' }))}
          >
            <option value="">Sélectionner un fournisseur</option>
            {filteredProviders.map(provider => (
              <option key={provider.id} value={provider.id}>
                {provider.name} ({provider.type})
                {!provider.isConfigured && ' - Non configuré'}
              </option>
            ))}
          </select>
        </div>

        {/* Model Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Modèle
          </label>
          <select
            className="input-field"
            value={localConfig.modelName}
            onChange={(e) => setLocalConfig(prev => ({ ...prev, modelName: e.target.value }))}
            disabled={!selectedProvider}
          >
            <option value="">Sélectionner un modèle</option>
            {availableModels.map(model => (
              <option key={model} value={model}>{model}</option>
            ))}
          </select>
        </div>

        {/* Temperature */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Température ({localConfig.temperature})
          </label>
          <input
            type="range"
            min="0"
            max="2"
            step="0.1"
            className="w-full"
            value={localConfig.temperature}
            onChange={(e) => setLocalConfig(prev => ({ ...prev, temperature: parseFloat(e.target.value) }))}
          />
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>Précis</span>
            <span>Créatif</span>
          </div>
        </div>

        {/* Max Tokens */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Tokens Maximum
          </label>
          <input
            type="number"
            className="input-field"
            value={localConfig.maxTokens}
            onChange={(e) => setLocalConfig(prev => ({ ...prev, maxTokens: parseInt(e.target.value) }))}
            min="100"
            max="32000"
          />
        </div>

        {/* Fallback Provider */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Fournisseur de Secours
          </label>
          <select
            className="input-field"
            value={localConfig.fallbackProviderId}
            onChange={(e) => setLocalConfig(prev => ({ ...prev, fallbackProviderId: e.target.value }))}
          >
            <option value="">Aucun</option>
            {filteredProviders
              .filter(p => p.id !== localConfig.providerId)
              .map(provider => (
                <option key={provider.id} value={provider.id}>
                  {provider.name} ({provider.type})
                </option>
              ))}
          </select>
        </div>

        {/* Max Retries */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Tentatives Maximum
          </label>
          <input
            type="number"
            className="input-field"
            value={localConfig.maxRetries}
            onChange={(e) => setLocalConfig(prev => ({ ...prev, maxRetries: parseInt(e.target.value) }))}
            min="1"
            max="10"
          />
        </div>
      </div>

      {/* Custom Prompt */}
      <div className="mt-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Prompt Personnalisé (optionnel)
        </label>
        <textarea
          className="input-field"
          rows={3}
          value={localConfig.customPrompt}
          onChange={(e) => setLocalConfig(prev => ({ ...prev, customPrompt: e.target.value }))}
          placeholder="Instructions spécifiques pour cette tâche..."
        />
      </div>

      {/* Advanced Settings */}
      <details className="mt-6">
        <summary className="cursor-pointer text-sm font-medium text-gray-700 mb-4">
          Paramètres Avancés
        </summary>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Top P</label>
            <input
              type="number"
              className="input-field"
              value={localConfig.topP}
              onChange={(e) => setLocalConfig(prev => ({ ...prev, topP: parseFloat(e.target.value) }))}
              min="0"
              max="1"
              step="0.1"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Frequency Penalty</label>
            <input
              type="number"
              className="input-field"
              value={localConfig.frequencyPenalty}
              onChange={(e) => setLocalConfig(prev => ({ ...prev, frequencyPenalty: parseFloat(e.target.value) }))}
              min="-2"
              max="2"
              step="0.1"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Presence Penalty</label>
            <input
              type="number"
              className="input-field"
              value={localConfig.presencePenalty}
              onChange={(e) => setLocalConfig(prev => ({ ...prev, presencePenalty: parseFloat(e.target.value) }))}
              min="-2"
              max="2"
              step="0.1"
            />
          </div>
        </div>
      </details>

      {/* Save Button */}
      <div className="mt-6 flex justify-end">
        <button
          onClick={handleSave}
          disabled={isLoading || !localConfig.providerId || !localConfig.modelName}
          className="btn-primary"
        >
          {isLoading ? <LoadingSpinner size="sm" /> : 'Sauvegarder'}
        </button>
      </div>
    </div>
  );
};

export default function AIConfigurationPage() {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  const [providers, setProviders] = useState<AIProviderWithDetails[]>([]);
  const [configurations, setConfigurations] = useState<Record<AITaskType, AITaskConfiguration | null>>({} as any);
  const [isLoadingData, setIsLoadingData] = useState(true);

  const aiTasks: AITaskType[] = [
    'research',
    'content_analysis', 
    'script_generation',
    'translation',
    'image_generation',
    'image_analysis',
    'metadata_generation',
    'voice_script',
    'video_planning',
    'transition_planning',
    'animation_planning',
    'seo_optimization',
    'hashtag_generation',
    'thumbnail_text',
    'fallback'
  ];

  useEffect(() => {
    if (user) {
      loadData();
    }
  }, [user]);

  const loadData = async () => {
    setIsLoadingData(true);
    try {
      // Load providers
      const providersResponse = await fetch('/api/ai-providers');
      const providersData = await providersResponse.json();
      
      // Load configurations
      const configResponse = await fetch('/api/settings/ai-tasks');
      const configData = await configResponse.json();
      
      setProviders(providersData.data || []);
      
      // Convert array to object keyed by taskType
      const configMap: Record<AITaskType, AITaskConfiguration | null> = {} as any;
      aiTasks.forEach(task => {
        configMap[task] = configData.data?.find((c: AITaskConfiguration) => c.taskType === task) || null;
      });
      setConfigurations(configMap);
      
    } catch (error) {
      console.error('Error loading AI configuration data:', error);
    } finally {
      setIsLoadingData(false);
    }
  };

  const handleSaveConfiguration = async (taskType: AITaskType, config: Partial<AITaskConfiguration>) => {
    try {
      const response = await fetch('/api/settings/ai-tasks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...config,
          taskType
        }),
      });

      if (response.ok) {
        await loadData(); // Reload data
      } else {
        const error = await response.json();
        alert(`Erreur: ${error.message || 'Échec de la sauvegarde'}`);
      }
    } catch (error) {
      console.error('Error saving configuration:', error);
      alert('Échec de la sauvegarde. Veuillez réessayer.');
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!user) {
    router.push('/auth/login');
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container-wide py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Configuration IA Granulaire</h1>
          <p className="mt-2 text-gray-600">
            Configurez précisément quel fournisseur et modèle d'IA utiliser pour chaque tâche spécifique
          </p>
        </div>

        {isLoadingData ? (
          <div className="flex justify-center py-12">
            <LoadingSpinner size="lg" />
          </div>
        ) : (
          <div className="space-y-6">
            {aiTasks.map(taskType => (
              <TaskConfiguration
                key={taskType}
                taskType={taskType}
                config={configurations[taskType]}
                providers={providers}
                onSave={(config) => handleSaveConfiguration(taskType, config)}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
