'use client';

import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

export default function SettingsPage() {
  const { user, isLoading } = useAuth();
  const router = useRouter();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!user) {
    router.push('/auth/login');
    return null;
  }

  const settingsCategories = [
    {
      title: 'Fournisseurs d\'IA',
      description: 'Gérez vos fournisseurs d\'intelligence artificielle et leurs clés API',
      href: '/settings/ai-providers',
      icon: '🤖',
      color: 'bg-blue-50 border-blue-200 hover:bg-blue-100'
    },
    {
      title: 'Configuration Granulaire IA',
      description: 'Configurez précisément quel modèle d\'IA utiliser pour chaque tâche spécifique',
      href: '/settings/ai-configuration',
      icon: '⚙️',
      color: 'bg-purple-50 border-purple-200 hover:bg-purple-100'
    },
    {
      title: 'Génération Vidéo',
      description: 'Paramètres avancés pour la génération de vidéos : images, transitions, animations, audio',
      href: '/settings/video-generation',
      icon: '🎬',
      color: 'bg-green-50 border-green-200 hover:bg-green-100'
    },
    {
      title: 'Voix et Audio',
      description: 'Configuration des voix de synthèse et paramètres audio',
      href: '/settings/voice',
      icon: '🎤',
      color: 'bg-orange-50 border-orange-200 hover:bg-orange-100'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container-wide py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Paramètres</h1>
          <p className="mt-2 text-gray-600">
            Configurez tous les aspects de votre générateur de contenu vidéo
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {settingsCategories.map((category) => (
            <div
              key={category.href}
              onClick={() => router.push(category.href)}
              className={`p-6 rounded-lg border-2 cursor-pointer transition-all duration-200 ${category.color}`}
            >
              <div className="flex items-start space-x-4">
                <div className="text-3xl">{category.icon}</div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {category.title}
                  </h3>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {category.description}
                  </p>
                </div>
                <div className="text-gray-400">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-12 bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Guide de Configuration</h2>
          <div className="space-y-4 text-sm text-gray-600">
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-semibold text-xs mt-0.5">1</div>
              <div>
                <strong>Fournisseurs d'IA :</strong> Commencez par ajouter vos fournisseurs d'IA et leurs clés API (OpenAI, Google, Groq, etc.)
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center text-purple-600 font-semibold text-xs mt-0.5">2</div>
              <div>
                <strong>Configuration Granulaire :</strong> Définissez quel modèle utiliser pour chaque tâche (recherche, analyse, génération de script, etc.)
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center text-green-600 font-semibold text-xs mt-0.5">3</div>
              <div>
                <strong>Génération Vidéo :</strong> Configurez les paramètres de qualité, transitions, animations et audio pour vos vidéos
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-orange-100 rounded-full flex items-center justify-center text-orange-600 font-semibold text-xs mt-0.5">4</div>
              <div>
                <strong>Voix et Audio :</strong> Sélectionnez et configurez les voix de synthèse pour la narration de vos vidéos
              </div>
            </div>
          </div>
        </div>

        <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <div className="text-yellow-600">
              <svg className="w-5 h-5 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div>
              <h3 className="text-sm font-medium text-yellow-800">Configuration Recommandée</h3>
              <p className="text-sm text-yellow-700 mt-1">
                Pour une expérience optimale, configurez au minimum un fournisseur d'IA avec une clé API valide avant de commencer à générer du contenu.
                La configuration granulaire vous permet d'optimiser les coûts en utilisant différents modèles selon la complexité de chaque tâche.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
