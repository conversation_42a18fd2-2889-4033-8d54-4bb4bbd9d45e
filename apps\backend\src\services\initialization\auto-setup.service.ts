import { prisma } from '@piknowkyo/database/client';
import { logger } from '../../config/logger';
import { AIProviderService } from '../ai/ai-provider.service';
import { VoiceService } from '../voice/voice.service';



export class AutoSetupService {
  private aiProviderService: AIProviderService;
  private voiceService: VoiceService;

  constructor() {
    this.aiProviderService = new AIProviderService();
    this.voiceService = new VoiceService();
  }

  async initializeDefaultProviders(): Promise<void> {
    logger.info('🚀 Starting automatic provider initialization...');

    try {
      // Initialize AI providers
      await this.initializeAIProviders();
      
      // Initialize TTS providers
      await this.initializeTTSProviders();
      
      // Initialize default configurations
      await this.initializeDefaultConfigurations();

      logger.info('✅ Automatic provider initialization completed successfully');
    } catch (error) {
      logger.error('❌ Failed to initialize providers automatically:', error);
      throw error;
    }
  }

  private async initializeAIProviders(): Promise<void> {
    logger.info('Initializing AI providers from environment variables...');

    const providers = [
      {
        name: 'Google AI',
        type: 'GOOGLE' as const,
        apiKeys: this.getApiKeysFromEnv('GOOGLE_AI_API_KEY'),
        baseUrl: 'https://generativelanguage.googleapis.com'
      },
      {
        name: 'OpenRouter',
        type: 'OPENROUTER' as const,
        apiKeys: this.getApiKeysFromEnv('OPENROUTER_API_KEY'),
        baseUrl: 'https://openrouter.ai/api'
      },
      {
        name: 'Groq',
        type: 'GROQ' as const,
        apiKeys: this.getApiKeysFromEnv('GROQ_API_KEY'),
        baseUrl: 'https://api.groq.com/openai'
      },
      {
        name: 'Mistral AI',
        type: 'MISTRAL' as const,
        apiKeys: this.getApiKeysFromEnv('MISTRAL_API_KEY'),
        baseUrl: 'https://api.mistral.ai'
      }
    ];

    for (const providerConfig of providers) {
      if (providerConfig.apiKeys.length > 0) {
        try {
          logger.info(`Setting up ${providerConfig.name}...`);
          
          const provider = await this.aiProviderService.upsertProvider({
            name: providerConfig.name,
            type: providerConfig.type,
            baseUrl: providerConfig.baseUrl,
            isCustom: false,
            apiKeys: providerConfig.apiKeys
          });

          // Fetch and update models
          await this.aiProviderService.fetchModels(provider.id);
          
          logger.info(`✅ ${providerConfig.name} initialized successfully`);
        } catch (error) {
          logger.warn(`⚠️ Failed to initialize ${providerConfig.name}:`, error);
        }
      } else {
        logger.info(`⏭️ Skipping ${providerConfig.name} - no API keys found in environment`);
      }
    }
  }

  private async initializeTTSProviders(): Promise<void> {
    logger.info('Initializing TTS providers from environment variables...');

    const providers = [
      {
        name: 'Azure Speech',
        type: 'AZURE' as const,
        apiKeys: this.getApiKeysFromEnv('AZURE_SPEECH_KEY'),
        config: {
          region: process.env.AZURE_SPEECH_REGION || 'eastus'
        }
      },
      {
        name: 'ElevenLabs',
        type: 'ELEVENLABS' as const,
        apiKeys: this.getApiKeysFromEnv('ELEVENLABS_API_KEY')
      },
      {
        name: 'Google TTS',
        type: 'GOOGLE' as const,
        apiKeys: this.getApiKeysFromEnv('GOOGLE_TTS_API_KEY')
      }
    ];

    for (const providerConfig of providers) {
      if (providerConfig.apiKeys.length > 0) {
        try {
          logger.info(`Setting up ${providerConfig.name}...`);
          
          const provider = await this.voiceService.upsertProvider({
            name: providerConfig.name,
            type: providerConfig.type,
            isCustom: false,
            config: providerConfig.config,
            apiKeys: providerConfig.apiKeys
          });

          // Fetch and update voices
          await this.voiceService.fetchVoices(provider.id);
          
          logger.info(`✅ ${providerConfig.name} initialized successfully`);
        } catch (error) {
          logger.warn(`⚠️ Failed to initialize ${providerConfig.name}:`, error);
        }
      } else {
        logger.info(`⏭️ Skipping ${providerConfig.name} - no API keys found in environment`);
      }
    }
  }

  private async initializeDefaultConfigurations(): Promise<void> {
    logger.info('Setting up default AI task configurations...');

    // Get the first available AI provider
    const aiProvider = await prisma.aIProvider.findFirst({
      where: { isActive: true }
    });

    if (!aiProvider) {
      logger.warn('No AI providers available for default configuration');
      return;
    }

    // Get the first available model for this provider
    const model = await prisma.aIModel.findFirst({
      where: { 
        providerId: aiProvider.id,
        isActive: true 
      }
    });

    if (!model) {
      logger.warn(`No models available for provider ${aiProvider.name}`);
      return;
    }

    // Default task configurations
    const defaultTasks = [
      {
        taskType: 'content_generation',
        temperature: 0.7,
        maxTokens: 2000,
        customPrompt: 'Generate engaging and informative content for video creation.'
      },
      {
        taskType: 'script_writing',
        temperature: 0.8,
        maxTokens: 1500,
        customPrompt: 'Write compelling video scripts that engage viewers.'
      },
      {
        taskType: 'title_generation',
        temperature: 0.9,
        maxTokens: 100,
        customPrompt: 'Create catchy and SEO-friendly video titles.'
      },
      {
        taskType: 'description_generation',
        temperature: 0.7,
        maxTokens: 500,
        customPrompt: 'Write detailed and engaging video descriptions.'
      }
    ];

    // Create a default user settings entry for system-wide defaults
    const systemUser = await this.getOrCreateSystemUser();
    
    let userSettings = await prisma.userSettings.findUnique({
      where: { userId: systemUser.id }
    });

    if (!userSettings) {
      userSettings = await prisma.userSettings.create({
        data: { userId: systemUser.id }
      });
    }

    for (const taskConfig of defaultTasks) {
      try {
        await prisma.userAISettings.upsert({
          where: {
            userSettingsId_taskType: {
              userSettingsId: userSettings.id,
              taskType: taskConfig.taskType
            }
          },
          create: {
            userSettingsId: userSettings.id,
            taskType: taskConfig.taskType,
            aiProviderId: aiProvider.id,
            modelName: model.name,
            temperature: taskConfig.temperature,
            maxTokens: taskConfig.maxTokens,
            topP: 1.0,
            frequencyPenalty: 0.0,
            presencePenalty: 0.0,
            customPrompt: taskConfig.customPrompt,
            priority: 1,
            maxRetries: 3,
            retryDelay: 5,
            requiresInternetAccess: false,
            isActive: true
          },
          update: {
            aiProviderId: aiProvider.id,
            modelName: model.name,
            updatedAt: new Date()
          }
        });

        logger.info(`✅ Default configuration for ${taskConfig.taskType} created`);
      } catch (error) {
        logger.warn(`⚠️ Failed to create default configuration for ${taskConfig.taskType}:`, error);
      }
    }
  }

  private async getOrCreateSystemUser() {
    const systemEmail = '<EMAIL>';
    
    let systemUser = await prisma.user.findUnique({
      where: { email: systemEmail }
    });

    if (!systemUser) {
      systemUser = await prisma.user.create({
        data: {
          email: systemEmail,
          name: 'System Default',
          password: 'system-user-no-login', // This user cannot login
          isActive: false // System user, not for login
        }
      });
    }

    return systemUser;
  }

  private getApiKeysFromEnv(prefix: string): string[] {
    const keys: string[] = [];
    
    for (let i = 1; i <= 4; i++) {
      const key = process.env[`${prefix}_${i}`];
      if (key && key !== `your_${prefix.toLowerCase()}_${i}` && !key.startsWith('your_')) {
        keys.push(key);
      }
    }
    
    return keys;
  }
}
