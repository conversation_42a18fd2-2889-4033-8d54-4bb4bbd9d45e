import axios, { AxiosResponse } from 'axios';
import CryptoJ<PERSON> from 'crypto-js';
import { prisma } from '@piknowkyo/database/client';
import { logger } from '../../config/logger';
import { createError } from '../../middleware/error-handler';
import { ERROR_CODES } from '@piknowkyo/shared/constants';
import {
  TTSProvider,
  Voice,
  VoiceSettings,
  TTSProviderType,
  Language,
  VoiceGender,
  TTSSynthesisRequest,
  TTSSynthesisResponse,
  VoicePreviewRequest,
  VoicePreviewResponse
} from '@piknowkyo/shared/types';

export class VoiceService {
  private encryptionKey: string;

  constructor() {
    this.encryptionKey = process.env.ENCRYPTION_KEY || 'default-key-change-in-production';
  }

  // Encrypt API key
  private encryptApiKey(apiKey: string): string {
    return CryptoJS.AES.encrypt(apiKey, this.encryptionKey).toString();
  }

  // Decrypt API key
  private decryptApiKey(encryptedKey: string): string {
    const bytes = CryptoJS.AES.decrypt(encryptedKey, this.encryptionKey);
    return bytes.toString(CryptoJS.enc.Utf8);
  }

  // Get all active TTS providers
  async getProviders(): Promise<TTSProvider[]> {
    const providers = await prisma.tTSProvider.findMany({
      where: { isActive: true },
      include: {
        voices: {
          where: { isActive: true }
        },
        apiKeys: {
          where: { isActive: true },
          select: {
            id: true,
            keyName: true,
            isActive: true,
            lastUsed: true,
            usageCount: true
          }
        }
      }
    });

    return providers.map(provider => ({
      ...provider,
      type: provider.type as TTSProviderType,
      baseUrl: provider.baseUrl || '',
      supportedLanguages: ['ENGLISH'] as Language[], // Default values
      supportedFormats: ['mp3', 'wav'],
      maxCharacters: 5000
    }));
  }

  // Get provider by ID
  async getProvider(id: string): Promise<TTSProvider | null> {
    const provider = await prisma.tTSProvider.findUnique({
      where: { id },
      include: {
        voices: {
          where: { isActive: true }
        },
        apiKeys: {
          where: { isActive: true },
          select: {
            id: true,
            keyName: true,
            isActive: true,
            lastUsed: true,
            usageCount: true
          }
        }
      }
    });

    if (!provider) return null;

    return {
      ...provider,
      type: provider.type as TTSProviderType,
      baseUrl: provider.baseUrl || '',
      supportedLanguages: ['ENGLISH'] as Language[], // Default values
      supportedFormats: ['mp3', 'wav'],
      maxCharacters: 5000
    };
  }

  // Get voices by language
  async getVoicesByLanguage(language: Language): Promise<Voice[]> {
    const voices = await prisma.voice.findMany({
      where: {
        language,
        isActive: true,
        provider: {
          isActive: true
        }
      },
      include: {
        provider: true
      }
    });

    return voices.map(voice => ({
      ...voice,
      language: voice.language as Language,
      gender: voice.gender as VoiceGender,
      style: voice.style || undefined,
      previewUrl: voice.previewUrl || undefined
    }));
  }

  // Create or update TTS provider
  async upsertProvider(data: {
    name: string;
    type: TTSProviderType;
    baseUrl?: string;
    isCustom?: boolean;
    config?: Record<string, any>;
    apiKeys?: string[];
  }): Promise<TTSProvider> {
    const { apiKeys, config, ...providerData } = data;

    // Create or update provider
    const provider = await prisma.tTSProvider.upsert({
      where: { name: data.name },
      create: {
        ...providerData,
        isCustom: data.isCustom || false,
        config: config ? JSON.stringify(config) : null
      },
      update: {
        ...providerData,
        config: config ? JSON.stringify(config) : null,
        updatedAt: new Date()
      }
    });

    // Update API keys if provided
    if (apiKeys && apiKeys.length > 0) {
      // Remove existing keys
      await prisma.tTSProviderApiKey.deleteMany({
        where: { providerId: provider.id }
      });

      // Add new keys
      const keyData = apiKeys.map((key, index) => ({
        providerId: provider.id,
        keyName: `key_${index + 1}`,
        encryptedKey: this.encryptApiKey(key)
      }));

      await prisma.tTSProviderApiKey.createMany({
        data: keyData
      });
    }

    return {
      ...provider,
      type: provider.type as TTSProviderType,
      baseUrl: provider.baseUrl || '',
      supportedLanguages: ['ENGLISH'] as Language[], // Default values
      supportedFormats: ['mp3', 'wav'],
      maxCharacters: 5000
    };
  }

  // Fetch voices from provider API
  async fetchVoices(providerId: string): Promise<Voice[]> {
    const provider = await this.getProvider(providerId);
    if (!provider) {
      throw createError('Provider not found', 404, ERROR_CODES.NOT_FOUND);
    }

    // Get raw provider data for config access
    const rawProvider = await prisma.tTSProvider.findUnique({
      where: { id: providerId }
    });

    const apiKey = await this.getActiveApiKey(providerId);
    if (!apiKey) {
      throw createError('No active API key found for provider', 400, ERROR_CODES.TTS_PROVIDER_ERROR);
    }

    try {
      let voices: any[] = [];

      switch (provider.type) {
        case 'AZURE':
          const azureConfig = rawProvider?.config ? JSON.parse(rawProvider.config) : {};
          voices = await this.fetchAzureVoices(apiKey, azureConfig?.region);
          break;
        case 'ELEVENLABS':
          voices = await this.fetchElevenLabsVoices(apiKey);
          break;
        case 'GOOGLE':
          voices = await this.fetchGoogleVoices(apiKey);
          break;
        case 'CUSTOM':
          voices = await this.fetchCustomProviderVoices(provider, apiKey);
          break;
        default:
          throw createError('Unsupported provider type', 400, ERROR_CODES.TTS_PROVIDER_ERROR);
      }

      // Update voices in database
      await this.updateProviderVoices(providerId, voices);

      const dbVoices = await prisma.voice.findMany({
        where: { providerId, isActive: true }
      });

      return dbVoices.map(voice => ({
        ...voice,
        language: voice.language as Language,
        gender: voice.gender as VoiceGender,
        style: voice.style || undefined,
        previewUrl: voice.previewUrl || undefined
      }));
    } catch (error) {
      logger.error(`Failed to fetch voices for provider ${provider.name}:`, error);
      throw createError(
        `Failed to fetch voices: ${error instanceof Error ? error.message : 'Unknown error'}`,
        500,
        ERROR_CODES.TTS_PROVIDER_ERROR
      );
    }
  }

  // Get active API key for provider
  private async getActiveApiKey(providerId: string): Promise<string | null> {
    const apiKey = await prisma.tTSProviderApiKey.findFirst({
      where: {
        providerId,
        isActive: true
      },
      orderBy: [
        { usageCount: 'asc' },
        { lastUsed: 'asc' }
      ]
    });

    if (!apiKey) return null;

    return this.decryptApiKey(apiKey.encryptedKey);
  }

  // Update API key usage
  private async updateApiKeyUsage(providerId: string, keyName: string): Promise<void> {
    await prisma.tTSProviderApiKey.updateMany({
      where: {
        providerId,
        keyName
      },
      data: {
        lastUsed: new Date(),
        usageCount: {
          increment: 1
        }
      }
    });
  }

  // Fetch Azure voices
  private async fetchAzureVoices(apiKey: string, region: string = 'eastus'): Promise<any[]> {
    const response = await axios.get(
      `https://${region}.tts.speech.microsoft.com/cognitiveservices/voices/list`,
      {
        headers: {
          'Ocp-Apim-Subscription-Key': apiKey
        }
      }
    );

    return response.data.filter((voice: any) => 
      ['fr-FR', 'en-US', 'es-ES'].includes(voice.Locale)
    );
  }

  // Fetch ElevenLabs voices
  private async fetchElevenLabsVoices(apiKey: string): Promise<any[]> {
    const response = await axios.get(
      'https://api.elevenlabs.io/v1/voices',
      {
        headers: {
          'xi-api-key': apiKey
        }
      }
    );

    return response.data.voices || [];
  }

  // Fetch Google TTS voices
  private async fetchGoogleVoices(apiKey: string): Promise<any[]> {
    const response = await axios.get(
      'https://texttospeech.googleapis.com/v1/voices',
      {
        headers: {
          'Authorization': `Bearer ${apiKey}`
        }
      }
    );

    return response.data.voices?.filter((voice: any) => 
      voice.languageCodes.some((code: string) => 
        ['fr-FR', 'en-US', 'es-ES'].includes(code)
      )
    ) || [];
  }

  // Fetch custom provider voices
  private async fetchCustomProviderVoices(provider: TTSProvider, apiKey: string): Promise<any[]> {
    // Get raw provider data for config access
    const rawProvider = await prisma.tTSProvider.findUnique({
      where: { id: provider.id }
    });

    const config = rawProvider?.config ? JSON.parse(rawProvider.config) : {};
    if (!config?.voicesEndpoint) {
      throw createError('Voices endpoint not configured for custom provider', 400, ERROR_CODES.TTS_PROVIDER_ERROR);
    }

    const response = await axios.get(
      `${provider.baseUrl}${config.voicesEndpoint}`,
      {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          ...config.headers
        }
      }
    );

    return response.data.voices || response.data.data || [];
  }

  // Update provider voices in database
  private async updateProviderVoices(providerId: string, voices: any[]): Promise<void> {
    // Deactivate existing voices
    await prisma.voice.updateMany({
      where: { providerId },
      data: { isActive: false }
    });

    // Upsert new voices
    for (const voice of voices) {
      const voiceId = voice.voice_id || voice.VoiceId || voice.name || voice.id;
      const name = voice.name || voice.DisplayName || voice.LocalName || voiceId;
      
      // Determine language
      let language: Language = 'ENGLISH';
      const locale = voice.Locale || voice.language_code || voice.languageCodes?.[0] || '';

      if (locale.startsWith('fr')) language = 'FRENCH';
      else if (locale.startsWith('es')) language = 'SPANISH';
      else if (locale.startsWith('en')) language = 'ENGLISH';

      // Determine gender
      let gender: VoiceGender = 'NEUTRAL';
      const genderStr = voice.Gender || voice.gender || '';

      if (genderStr.toLowerCase().includes('male')) gender = 'MALE';
      else if (genderStr.toLowerCase().includes('female')) gender = 'FEMALE';

      await prisma.voice.upsert({
        where: {
          providerId_voiceId: {
            providerId,
            voiceId
          }
        },
        create: {
          providerId,
          voiceId,
          name,
          language,
          gender,
          style: voice.StyleList?.[0] || voice.style,
          previewUrl: voice.preview_url,
          isActive: true
        },
        update: {
          name,
          language,
          gender,
          style: voice.StyleList?.[0] || voice.style,
          previewUrl: voice.preview_url,
          isActive: true,
          updatedAt: new Date()
        }
      });
    }
  }

  // Generate voice preview
  async generatePreview(request: VoicePreviewRequest): Promise<VoicePreviewResponse> {
    const dbVoice = await prisma.voice.findFirst({
      where: {
        voiceId: request.voiceId,
        provider: {
          name: request.provider,
          isActive: true
        }
      },
      include: {
        provider: true
      }
    });

    if (!dbVoice) {
      throw createError('Voice not found', 404, ERROR_CODES.NOT_FOUND);
    }

    // Convert to typed Voice object
    const voice: Voice = {
      ...dbVoice,
      language: dbVoice.language as Language,
      gender: dbVoice.gender as VoiceGender,
      previewUrl: dbVoice.previewUrl || undefined
    };

    // Convert to typed TTSProvider object
    const provider: TTSProvider = {
      ...dbVoice.provider,
      type: dbVoice.provider.type as TTSProviderType,
      baseUrl: dbVoice.provider.baseUrl || '',
      supportedLanguages: ['ENGLISH'] as Language[],
      supportedFormats: ['mp3', 'wav'],
      maxCharacters: 5000
    };

    const apiKey = await this.getActiveApiKey(voice.providerId);
    if (!apiKey) {
      throw createError('No active API key found for provider', 400, ERROR_CODES.TTS_PROVIDER_ERROR);
    }

    try {
      let audioUrl: string;
      let duration: number = 0;

      switch (provider.type) {
        case 'AZURE':
          audioUrl = await this.generateAzurePreview(apiKey, voice, request);
          break;
        case 'ELEVENLABS':
          audioUrl = await this.generateElevenLabsPreview(apiKey, voice, request);
          break;
        case 'GOOGLE':
          audioUrl = await this.generateGooglePreview(apiKey, voice, request);
          break;
        case 'CUSTOM':
          audioUrl = await this.generateCustomProviderPreview(provider, apiKey, voice, request);
          break;
        default:
          throw createError('Unsupported provider type', 400, ERROR_CODES.TTS_PROVIDER_ERROR);
      }

      // Update API key usage
      await this.updateApiKeyUsage(voice.providerId, 'key_1');

      return {
        audioUrl,
        duration
      };
    } catch (error) {
      logger.error(`Failed to generate voice preview:`, error);
      throw createError(
        `Failed to generate voice preview: ${error instanceof Error ? error.message : 'Unknown error'}`,
        500,
        ERROR_CODES.TTS_PROVIDER_ERROR
      );
    }
  }

  // Generate Azure preview (simplified implementation)
  private async generateAzurePreview(
    apiKey: string,
    voice: Voice,
    request: VoicePreviewRequest
  ): Promise<string> {
    // This would implement Azure Speech synthesis
    // For now, return a placeholder
    return 'https://example.com/preview.mp3';
  }

  // Generate ElevenLabs preview
  private async generateElevenLabsPreview(
    apiKey: string,
    voice: Voice,
    request: VoicePreviewRequest
  ): Promise<string> {
    // This would implement ElevenLabs synthesis
    // For now, return a placeholder
    return 'https://example.com/preview.mp3';
  }

  // Generate Google TTS preview
  private async generateGooglePreview(
    apiKey: string,
    voice: Voice,
    request: VoicePreviewRequest
  ): Promise<string> {
    // This would implement Google TTS synthesis
    // For now, return a placeholder
    return 'https://example.com/preview.mp3';
  }

  // Generate custom provider preview
  private async generateCustomProviderPreview(
    provider: TTSProvider,
    apiKey: string,
    voice: Voice,
    request: VoicePreviewRequest
  ): Promise<string> {
    // This would implement custom provider synthesis
    // For now, return a placeholder
    return 'https://example.com/preview.mp3';
  }

  // Synthesize speech
  async synthesizeSpeech(request: TTSSynthesisRequest): Promise<TTSSynthesisResponse> {
    // This would implement full speech synthesis
    // For now, return a placeholder response
    return {
      audioUrl: 'https://example.com/audio.mp3',
      duration: 30,
      format: request.outputFormat || 'mp3',
      size: 1024000
    };
  }
}
