# Database Configuration
DATABASE_URL="file:./database/dev.db"

# JWT Secret for Authentication
JWT_SECRET="your_super_secret_jwt_key_change_this_in_production"

# =============================================================================
# AI PROVIDER CONFIGURATION
# Multiple API keys per provider for fallback support when hitting rate limits
# All models are fetched dynamically from provider APIs - no hardcoded models!
# =============================================================================

# Google AI (Gemini) - Primary AI Provider
# Models fetched from: https://generativelanguage.googleapis.com/v1/models
GOOGLE_AI_API_KEY_1="your_primary_google_ai_api_key"
GOOGLE_AI_API_KEY_2="your_secondary_google_ai_api_key"
GOOGLE_AI_API_KEY_3="your_tertiary_google_ai_api_key"
GOOGLE_AI_API_KEY_4="your_quaternary_google_ai_api_key"

# OpenRouter AI - Access to Multiple Models
# Models fetched from: https://openrouter.ai/api/v1/models
OPENROUTER_API_KEY_1="your_primary_openrouter_api_key"
OPENROUTER_API_KEY_2="your_secondary_openrouter_api_key"
OPENROUTER_API_KEY_3="your_tertiary_openrouter_api_key"
OPENROUTER_API_KEY_4="your_quaternary_openrouter_api_key"

# Groq AI - Fast Inference Provider
# Models fetched from: https://api.groq.com/openai/v1/models
GROQ_API_KEY_1="your_primary_groq_api_key"
GROQ_API_KEY_2="your_secondary_groq_api_key"
GROQ_API_KEY_3="your_tertiary_groq_api_key"
GROQ_API_KEY_4="your_quaternary_groq_api_key"

# Mistral AI - European AI Provider
# Models fetched from: https://api.mistral.ai/v1/models
MISTRAL_API_KEY_1="your_primary_mistral_api_key"
MISTRAL_API_KEY_2="your_secondary_mistral_api_key"
MISTRAL_API_KEY_3="your_tertiary_mistral_api_key"
MISTRAL_API_KEY_4="your_quaternary_mistral_api_key"

# =============================================================================
# CUSTOM AI PROVIDERS (User-configurable)
# Users can add unlimited custom providers through the dashboard
# =============================================================================

# Example: Custom Provider 1
# CUSTOM_PROVIDER_1_NAME="Your Custom Provider Name"
# CUSTOM_PROVIDER_1_BASE_URL="https://api.yourcustomprovider.com/v1"
# CUSTOM_PROVIDER_1_API_KEY_1="your_custom_provider_key_1"
# CUSTOM_PROVIDER_1_API_KEY_2="your_custom_provider_key_2"
# CUSTOM_PROVIDER_1_MODELS_ENDPOINT="/models"  # Endpoint to fetch available models
# CUSTOM_PROVIDER_1_CHAT_ENDPOINT="/chat/completions"  # Chat completion endpoint

# Example: Custom Provider 2
# CUSTOM_PROVIDER_2_NAME="Another Custom Provider"
# CUSTOM_PROVIDER_2_BASE_URL="https://api.anotherprovider.com"
# CUSTOM_PROVIDER_2_API_KEY_1="another_provider_key_1"
# CUSTOM_PROVIDER_2_API_KEY_2="another_provider_key_2"
# CUSTOM_PROVIDER_2_MODELS_ENDPOINT="/v1/models"
# CUSTOM_PROVIDER_2_CHAT_ENDPOINT="/v1/chat/completions"

# =============================================================================
# TEXT-TO-SPEECH CONFIGURATION
# All voices are fetched dynamically from provider APIs - no hardcoded voices!
# =============================================================================

# Azure Speech Services (Primary TTS)
# Voices fetched from: https://[region].tts.speech.microsoft.com/cognitiveservices/voices/list
AZURE_SPEECH_KEY_1="your_primary_azure_speech_key"
AZURE_SPEECH_KEY_2="your_secondary_azure_speech_key"
AZURE_SPEECH_KEY_3="your_tertiary_azure_speech_key"
AZURE_SPEECH_REGION="your_azure_region"

# ElevenLabs (Secondary TTS)
# Voices fetched from: https://api.elevenlabs.io/v1/voices
ELEVENLABS_API_KEY_1="your_primary_elevenlabs_api_key"
ELEVENLABS_API_KEY_2="your_secondary_elevenlabs_api_key"
ELEVENLABS_API_KEY_3="your_tertiary_elevenlabs_api_key"

# Google Cloud Text-to-Speech (Tertiary TTS)
# Voices fetched from: https://texttospeech.googleapis.com/v1/voices
GOOGLE_TTS_API_KEY_1="your_primary_google_tts_key"
GOOGLE_TTS_API_KEY_2="your_secondary_google_tts_key"
GOOGLE_TTS_API_KEY_3="your_tertiary_google_tts_key"

# =============================================================================
# CUSTOM TTS PROVIDERS (User-configurable)
# =============================================================================

# Example: Custom TTS Provider
# CUSTOM_TTS_1_NAME="Your Custom TTS Provider"
# CUSTOM_TTS_1_BASE_URL="https://api.yourcustomtts.com"
# CUSTOM_TTS_1_API_KEY_1="your_custom_tts_key_1"
# CUSTOM_TTS_1_API_KEY_2="your_custom_tts_key_2"
# CUSTOM_TTS_1_VOICES_ENDPOINT="/voices"  # Endpoint to fetch available voices
# CUSTOM_TTS_1_SYNTHESIS_ENDPOINT="/synthesize"  # Text-to-speech endpoint

# =============================================================================
# IMAGE GENERATION CONFIGURATION
# =============================================================================

# Stability AI (Primary Image Generation)
STABILITY_AI_API_KEY_1="your_primary_stability_ai_key"
STABILITY_AI_API_KEY_2="your_secondary_stability_ai_key"

# DALL-E via OpenRouter (Secondary Image Generation)
DALLE_VIA_OPENROUTER_KEY_1="your_dalle_openrouter_key_1"
DALLE_VIA_OPENROUTER_KEY_2="your_dalle_openrouter_key_2"

# Midjourney via API (if available)
MIDJOURNEY_API_KEY_1="your_midjourney_api_key_1"
MIDJOURNEY_API_KEY_2="your_midjourney_api_key_2"

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Server Configuration
PORT="5000"
NODE_ENV="development"
FRONTEND_URL="http://localhost:3001"

# Multi-language Settings (Default values - users can override in dashboard)
DEFAULT_LANGUAGE="ENGLISH"
SUPPORTED_LANGUAGES="FRENCH,ENGLISH,SPANISH"

# Content Creation Settings (Default values - users can override)
DEFAULT_VIDEO_DURATION="60"
CONTENT_OUTPUT_DIR="./storage"
TEMP_DIR="./storage/temp"

# =============================================================================
# RATE LIMITING AND FALLBACK CONFIGURATION
# =============================================================================

# API Rate Limiting
MAX_REQUESTS_PER_MINUTE="100"
MAX_VIDEOS_PER_DAY="50"

# Fallback Configuration
AI_PROVIDER_FALLBACK_ORDER="google,openrouter,groq,mistral"
TTS_PROVIDER_FALLBACK_ORDER="azure,elevenlabs,google"

# Retry Configuration
MAX_RETRIES_PER_PROVIDER="3"
RETRY_DELAY_SECONDS="5"

# =============================================================================
# SOCIAL MEDIA PLATFORM CREDENTIALS
# =============================================================================

# YouTube
YOUTUBE_CLIENT_ID="your_youtube_client_id"
YOUTUBE_CLIENT_SECRET="your_youtube_client_secret"

# TikTok
TIKTOK_CLIENT_ID="your_tiktok_client_id"
TIKTOK_CLIENT_SECRET="your_tiktok_client_secret"

# Instagram
INSTAGRAM_CLIENT_ID="your_instagram_client_id"
INSTAGRAM_CLIENT_SECRET="your_instagram_client_secret"

# =============================================================================
# NOTIFICATION SERVICES
# =============================================================================

# Email Notifications
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your_email_password"

# Push Notifications
PUSHOVER_USER_KEY="your_pushover_user_key"
PUSHOVER_APP_TOKEN="your_pushover_app_token"

# Slack Notifications
SLACK_WEBHOOK_URL="your_slack_webhook_url"

# =============================================================================
# EXTERNAL APIS FOR TREND RESEARCH
# =============================================================================

# Google Trends
GOOGLE_TRENDS_API_KEY="your_google_trends_api_key"

# Twitter/X API
TWITTER_BEARER_TOKEN="your_twitter_bearer_token"

# Reddit API
REDDIT_CLIENT_ID="your_reddit_client_id"
REDDIT_CLIENT_SECRET="your_reddit_client_secret"

# =============================================================================
# LOGGING AND MONITORING
# =============================================================================

# Logging Configuration
LOG_LEVEL="info"
LOG_FILE_PATH="./logs/app.log"

# Error Tracking
SENTRY_DSN="your_sentry_dsn_for_error_tracking"

# =============================================================================
# AUTOMATION SETTINGS
# =============================================================================

# Cron Job Configuration
ENABLE_AUTO_CREATION="true"
CREATION_SCHEDULE="0 */6 * * *"  # Every 6 hours
COMMENT_CHECK_SCHEDULE="*/15 * * * *"  # Every 15 minutes

# =============================================================================
# PERFORMANCE ANALYTICS
# =============================================================================

# Google Analytics
GOOGLE_ANALYTICS_API_KEY="your_google_analytics_api_key"

# YouTube Analytics
YOUTUBE_ANALYTICS_API_KEY="your_youtube_analytics_api_key"