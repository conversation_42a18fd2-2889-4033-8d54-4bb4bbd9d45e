{"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./", "baseUrl": "./", "paths": {"@piknowkyo/shared": ["../shared/dist/index.d.ts"], "@piknowkyo/shared/*": ["../shared/dist/*"], "@piknowkyo/database": ["../database/dist/index.d.ts"], "@piknowkyo/database/*": ["../database/dist/*"]}}, "include": ["**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}