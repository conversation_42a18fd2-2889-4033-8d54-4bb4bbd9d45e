# Production Deployment Guide

## Prerequisites for Production

- **Node.js 18+** (LTS recommended)
- **npm 9+** or **yarn 1.22+**
- **Git** for version control
- **PM2** for process management (recommended)
- **Nginx** for reverse proxy (optional but recommended)
- **SSL certificate** for HTTPS (recommended)

## Quick Start for Production

### 1. <PERSON>lone and Setup
```bash
# Clone the repository
git clone https://github.com/your-username/piknowkyo-generator.git
cd piknowkyo-generator

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env
# Edit .env with your API keys and configuration

# Initialize the database
npm run db:setup

# Build the packages
npm run build --workspace=packages/shared
npm run build --workspace=packages/database

# Start the development servers
npm run dev
```

### 2. Access the Application
- **Dashboard**: http://localhost:3001
- **API**: http://localhost:3000
- **Health Check**: http://localhost:3000/api/health

## Required Environment Variables

Create a production `.env` file with these essential variables:

```bash
# Production Environment
NODE_ENV=production
PORT=3000
FRONTEND_PORT=3001

# Database (SQLite for production)
DATABASE_URL="file:./database/production.db"

# Security
JWT_SECRET="your_super_secure_jwt_secret_minimum_32_characters"
BCRYPT_ROUNDS=12

# API Keys (Required for functionality)
GOOGLE_AI_API_KEY_1="your_google_ai_api_key"
OPENROUTER_API_KEY_1="your_openrouter_api_key"
GROQ_API_KEY_1="your_groq_api_key"
MISTRAL_API_KEY_1="your_mistral_api_key"

# TTS Services
AZURE_SPEECH_KEY_1="your_azure_speech_key"
AZURE_SPEECH_REGION="your_azure_region"
ELEVENLABS_API_KEY_1="your_elevenlabs_api_key"

# Storage Configuration
CONTENT_OUTPUT_DIR="./storage"
TEMP_DIR="./storage/temp"
MAX_FILE_SIZE=104857600  # 100MB

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100

# CORS (adjust for your domain)
ALLOWED_ORIGINS="https://yourdomain.com,https://www.yourdomain.com"
```

## Production Build Process

```bash
# Install dependencies
npm ci --production=false

# Build all packages and applications
npm run build

# Generate Prisma client
npm run db:generate

# Initialize production database
npm run db:setup

# Create storage directories
mkdir -p storage/{videos,temp,uploads}/{french,english,spanish}
chmod 755 -R storage/

# Start production servers
npm run start
```

## Process Management with PM2

For production deployments, use PM2 for process management:

```bash
# Install PM2 globally
npm install -g pm2

# Create PM2 ecosystem file
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [
    {
      name: 'piknowkyo-backend',
      script: 'apps/backend/dist/index.js',
      instances: 'max',
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      max_memory_restart: '1G'
    },
    {
      name: 'piknowkyo-frontend',
      script: 'apps/frontend/server.js',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'production',
        PORT: 3001
      },
      max_memory_restart: '512M'
    }
  ]
};
EOF

# Start applications
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save

# Setup PM2 to start on system boot
pm2 startup
```

## Health Monitoring

The application includes built-in health check endpoints:

- **Health Check**: `GET /api/health` - Overall system health
- **Readiness Check**: `GET /api/health/ready` - Application readiness
- **Liveness Check**: `GET /api/health/live` - Basic liveness probe

## Production Checklist

Before going live, ensure:

- [ ] **Environment variables** are properly configured
- [ ] **Database** is initialized and backed up
- [ ] **API keys** are valid and have sufficient quotas
- [ ] **Storage directories** have proper permissions
- [ ] **Process management** (PM2) is configured
- [ ] **Health checks** are responding correctly
- [ ] **Rate limiting** is configured appropriately
- [ ] **CORS** settings match your domain
- [ ] **SSL certificate** is installed (if using HTTPS)
- [ ] **Firewall** allows necessary ports (80, 443, 3000, 3001)

## Troubleshooting

### Common Issues

1. **TypeScript Compilation Errors**
   - Ensure all packages are built: `npm run build --workspace=packages/shared && npm run build --workspace=packages/database`
   - Check that Prisma client is generated: `npm run db:generate`

2. **Database Connection Issues**
   - Verify DATABASE_URL in .env file
   - Ensure database directory exists and is writable
   - Run database setup: `npm run db:setup`

3. **API Key Issues**
   - Verify all required API keys are set in .env
   - Check API key quotas and limits
   - Test individual providers through the dashboard

4. **Storage Permission Issues**
   - Ensure storage directories exist: `mkdir -p storage/{videos,temp,uploads}/{french,english,spanish}`
   - Set proper permissions: `chmod 755 -R storage/`

5. **Port Conflicts**
   - Check if ports 3000 and 3001 are available
   - Modify PORT and FRONTEND_PORT in .env if needed
   - Update firewall rules accordingly

For more detailed troubleshooting, check the application logs and health check endpoints.
