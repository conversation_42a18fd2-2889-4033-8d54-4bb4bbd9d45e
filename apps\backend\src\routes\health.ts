import { Router, Request, Response } from 'express';
import { prisma } from '@piknowkyo/database/client';
import { asyncHandler } from '../utils/async-handler';
import { HealthCheckResponse } from '@piknowkyo/shared/types';

const router = Router();

// Store server start time
const serverStartTime = Date.now();

// GET /health
router.get('/', asyncHandler(async (req: Request, res: Response) => {
  const healthCheck: HealthCheckResponse = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    uptime: Math.floor((Date.now() - serverStartTime) / 1000),
    services: {
      database: 'connected',
      storage: 'available'
    }
  };

  try {
    // Check database connection
    await prisma.$queryRaw`SELECT 1`;
    healthCheck.services.database = 'connected';
  } catch (error) {
    healthCheck.services.database = 'disconnected';
    healthCheck.status = 'unhealthy';
  }



  // Check storage (basic file system access)
  try {
    const fs = require('fs');
    const storageDir = process.env.STORAGE_DIR || './storage';

    // Check if storage directory exists and is writable
    if (fs.existsSync(storageDir)) {
      fs.accessSync(storageDir, fs.constants.W_OK);
      healthCheck.services.storage = 'available';
    } else {
      healthCheck.services.storage = 'unavailable';
      healthCheck.status = 'unhealthy';
    }
  } catch (error) {
    healthCheck.services.storage = 'unavailable';
    healthCheck.status = 'unhealthy';
  }

  // Set appropriate HTTP status code
  const statusCode = healthCheck.status === 'healthy' ? 200 : 503;

  res.status(statusCode).json(healthCheck);
}));

// GET /health/ready
router.get('/ready', asyncHandler(async (req: Request, res: Response) => {
  try {
    // Check if the application is ready to serve requests
    await prisma.$queryRaw`SELECT 1`;
    
    res.status(200).json({
      status: 'ready',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(503).json({
      status: 'not ready',
      timestamp: new Date().toISOString(),
      error: 'Database connection failed'
    });
  }
}));

// GET /health/live
router.get('/live', (req, res) => {
  // Simple liveness check
  res.status(200).json({
    status: 'alive',
    timestamp: new Date().toISOString(),
    uptime: Math.floor((Date.now() - serverStartTime) / 1000)
  });
});

export default router;
