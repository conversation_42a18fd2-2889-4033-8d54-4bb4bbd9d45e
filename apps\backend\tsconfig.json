{"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./src", "baseUrl": "./src", "paths": {"@shared/*": ["../../packages/shared/src/*"], "@database/*": ["../../packages/database/src/*"], "@piknowkyo/shared": ["../../packages/shared/dist/index.d.ts"], "@piknowkyo/shared/*": ["../../packages/shared/dist/*"], "@piknowkyo/database": ["../../packages/database/dist/index.d.ts"], "@piknowkyo/database/*": ["../../packages/database/dist/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}