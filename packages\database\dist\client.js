"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PrismaClient = exports.prisma = void 0;
const generated_1 = require("./generated");
// Create a singleton Prisma client instance
exports.prisma = globalThis.__prisma || new generated_1.PrismaClient({
    log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
});
// In development, store the client in the global variable to prevent multiple instances
if (process.env.NODE_ENV === 'development') {
    globalThis.__prisma = exports.prisma;
}
// Export the Prisma client class for direct instantiation if needed
var generated_2 = require("./generated");
Object.defineProperty(exports, "PrismaClient", { enumerable: true, get: function () { return generated_2.PrismaClient; } });
// Export all Prisma types
__exportStar(require("./generated"), exports);
//# sourceMappingURL=client.js.map