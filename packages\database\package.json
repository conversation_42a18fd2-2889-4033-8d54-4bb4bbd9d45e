{"name": "@piknowkyo/database", "version": "1.0.0", "description": "Database client and utilities for Piknowkyo Generator", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc && xcopy src\\generated dist\\generated /E /I /Y", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit"}, "dependencies": {"@prisma/client": "^5.0.0"}, "devDependencies": {"typescript": "^5.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./client": {"types": "./dist/client.d.ts", "default": "./dist/client.js"}, "./generated": {"types": "./src/generated/index.d.ts", "default": "./src/generated/index.js"}}}