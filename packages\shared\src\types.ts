// Base types
export type Language = 'ENGLISH' | 'FRENCH' | 'SPANISH' | 'GERMAN' | 'ITALIAN' | 'PORTUGUESE' | 'DUTCH' | 'RUSSIAN' | 'CHINESE' | 'JAPANESE' | 'KOREAN' | 'ARABIC';

export type UserRole = 'USER' | 'ADMIN' | 'SUPER_ADMIN';

export type VideoProjectStatus = 'DRAFT' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';

export type VoiceGender = 'MALE' | 'FEMALE' | 'NEUTRAL';

export type AIProviderType = 'OPENAI' | 'ANTHROPIC' | 'GOOGLE' | 'MISTRAL' | 'COHERE' | 'OPENROUTER' | 'GROQ' | 'CUSTOM';

export type TTSProviderType = 'OPENAI' | 'ELEVENLABS' | 'GOOGLE' | 'AZURE' | 'AWS' | 'CUSTOM';

// User types
export interface User {
  id: string;
  email: string;
  name?: string;
  role: UserRole;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserSettings {
  id: string;
  userId: string;
  defaultLanguage: Language;
  enableAutoCreation: boolean;
  maxVideosPerDay: number;
  enableNotifications: boolean;
  contentOutputDir: string;
  defaultVideoDuration: number;
}

// AI Task Types for granular configuration
export type AITaskType =
  | 'research'           // Internet research and trend analysis
  | 'content_analysis'   // Analyzing existing content
  | 'script_generation'  // Creating video scripts
  | 'translation'        // Multi-language translation
  | 'image_generation'   // Creating images/thumbnails
  | 'image_analysis'     // Analyzing images for descriptions
  | 'metadata_generation' // Titles, descriptions, tags
  | 'voice_script'       // Voice-over script optimization
  | 'video_planning'     // Video structure and flow
  | 'transition_planning' // Video transitions
  | 'animation_planning'  // Animation sequences
  | 'seo_optimization'   // SEO content optimization
  | 'hashtag_generation' // Social media hashtags
  | 'thumbnail_text'     // Thumbnail text generation
  | 'fallback';          // Fallback for failed requests

// AI Provider Configuration for specific tasks
export interface AITaskConfiguration {
  id: string;
  taskType: AITaskType;
  providerId: string;
  modelName: string;
  temperature: number;
  maxTokens: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  customPrompt?: string;
  fallbackProviderId?: string;
  fallbackModelName?: string;
  isActive: boolean;
  priority: number; // For fallback ordering
  requiresInternetAccess?: boolean; // For research tasks
  supportedLanguages?: Language[];
  estimatedCostPer1k?: number;
  maxRetries: number;
  retryDelay: number; // in seconds
}

// AI Provider types
export interface AIProvider {
  id: string;
  name: string;
  type: AIProviderType;
  baseUrl: string;
  isActive: boolean;
  isCustom?: boolean;
  config?: Record<string, any>;
  supportedFeatures?: string[];
  rateLimit?: number;
  hasInternetAccess?: boolean; // For research capabilities
  supportedTaskTypes?: AITaskType[];
  costPer1kTokens?: number;
  maxContextLength?: number;
  createdAt: Date;
  updatedAt: Date;
}

// Enhanced AI Provider with API Keys and Models
export interface AIProviderWithDetails extends AIProvider {
  apiKeys: AIProviderApiKey[];
  models: AIModel[];
  isConfigured: boolean; // Has valid API keys
  lastHealthCheck?: Date;
  healthStatus?: 'healthy' | 'degraded' | 'unhealthy';
}

export interface AIProviderApiKey {
  id: string;
  keyName: string;
  isActive: boolean;
  lastUsed?: Date;
  usageCount: number;
  rateLimit?: number;
  costLimit?: number; // Monthly cost limit
  createdAt: Date;
}

export interface AIModel {
  id: string;
  providerId: string;
  name: string;
  displayName: string;
  description?: string;
  contextLength: number;
  inputCostPer1k: number;
  outputCostPer1k: number;
  isActive: boolean;
  capabilities: string[];
  supportedTaskTypes?: AITaskType[];
  hasInternetAccess?: boolean;
  supportedLanguages?: Language[];
  maxOutputTokens?: number;
  isMultimodal?: boolean; // Supports images/vision
  supportsStreaming?: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Video Generation Configuration
export interface VideoGenerationConfig {
  id: string;
  userId: string;
  // Image Generation Settings
  imageProvider: string;
  imageModel: string;
  imageStyle: 'realistic' | 'artistic' | 'cartoon' | 'cinematic' | 'custom';
  imageResolution: '1024x1024' | '1024x1792' | '1792x1024' | 'custom';
  imageQuality: 'standard' | 'hd' | 'ultra';

  // Video Assembly Settings
  transitionType: 'fade' | 'slide' | 'zoom' | 'dissolve' | 'wipe' | 'custom';
  transitionDuration: number; // in seconds
  animationType: 'none' | 'ken_burns' | 'parallax' | 'zoom_in' | 'zoom_out' | 'custom';
  animationIntensity: 'subtle' | 'moderate' | 'dramatic';

  // Audio Settings
  backgroundMusic: boolean;
  musicVolume: number; // 0-100
  voiceVolume: number; // 0-100
  audioFadeIn: number; // seconds
  audioFadeOut: number; // seconds

  // Timing Settings
  imageDisplayDuration: number; // seconds per image
  textDisplayDuration: number; // seconds per text overlay
  totalVideoDuration?: number; // max duration

  // Quality Settings
  videoResolution: '720p' | '1080p' | '4k';
  videoFrameRate: 24 | 30 | 60;
  videoBitrate: 'auto' | 'low' | 'medium' | 'high' | 'ultra';

  createdAt: Date;
  updatedAt: Date;
}

export interface ChatCompletionRequest {
  model: string;
  messages: Array<{
    role: 'system' | 'user' | 'assistant';
    content: string;
  }>;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  stream?: boolean;
}

export interface ChatCompletionResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: {
      role: string;
      content: string;
    };
    finishReason: string;
  }>;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

export interface ModelFetchResponse {
  models: AIModel[];
  total: number;
}

// TTS Provider types
export interface TTSProvider {
  id: string;
  name: string;
  type: TTSProviderType;
  baseUrl: string;
  isActive: boolean;
  supportedLanguages: Language[];
  supportedFormats: string[];
  maxCharacters: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface Voice {
  id: string;
  providerId: string;
  voiceId: string;
  name: string;
  language: Language;
  gender: VoiceGender;
  description?: string;
  previewUrl?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface VoiceSettings {
  voiceId: string;
  speed: number;
  pitch: number;
  volume: number;
  stability?: number;
  similarityBoost?: number;
}

export interface TTSSynthesisRequest {
  text: string;
  voiceId: string;
  settings: VoiceSettings;
  format?: string;
  outputFormat?: string;
}

export interface TTSSynthesisResponse {
  audioUrl: string;
  duration: number;
  format: string;
  size: number;
}

export interface VoicePreviewRequest {
  voiceId: string;
  text?: string;
  provider?: string;
  language?: Language;
  settings?: any;
}

export interface VoicePreviewResponse {
  audioUrl: string;
  duration: number;
}

// Video Project types
export interface VideoProject {
  id: string;
  userId: string;
  title: string;
  description?: string;
  language: Language;
  status: VideoProjectStatus;
  duration?: number;
  outputPath?: string;
  thumbnailPath?: string;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Health check types
export interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  uptime: number;
  version: string;
  environment: string;
  services: {
    database: 'connected' | 'disconnected';
    redis?: 'connected' | 'disconnected';
    storage: 'available' | 'unavailable';
  };
}
