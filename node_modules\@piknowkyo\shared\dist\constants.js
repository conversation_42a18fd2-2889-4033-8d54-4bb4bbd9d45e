"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ALLOWED_FILE_TYPES = exports.API_LIMITS = exports.USER_ROLES = exports.VOICE_GENDER = exports.VIDEO_PROJECT_STATUS = exports.LANGUAGES = exports.TTS_PROVIDERS = exports.AI_PROVIDERS = exports.ERROR_CODES = void 0;
// Error codes used throughout the application
exports.ERROR_CODES = {
    // General errors
    INTERNAL_ERROR: 'INTERNAL_ERROR',
    VALIDATION_ERROR: 'VALIDATION_ERROR',
    NOT_FOUND: 'NOT_FOUND',
    ALREADY_EXISTS: 'ALREADY_EXISTS',
    FORBIDDEN: 'FORBIDDEN',
    // Authentication errors
    UNAUTHORIZED: 'UNAUTHORIZED',
    INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
    TOKEN_EXPIRED: 'TOKEN_EXPIRED',
    // API errors
    RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
    API_KEY_INVALID: 'API_KEY_INVALID',
    API_QUOTA_EXCEEDED: 'API_QUOTA_EXCEEDED',
    REQUEST_TOO_LARGE: 'REQUEST_TOO_LARGE',
    MISSING_REQUIRED_FIELD: 'MISSING_REQUIRED_FIELD',
    // File errors
    FILE_TOO_LARGE: 'FILE_TOO_LARGE',
    INVALID_FILE_TYPE: 'INVALID_FILE_TYPE',
    FILE_UPLOAD_ERROR: 'FILE_UPLOAD_ERROR',
    // Video processing errors
    VIDEO_PROCESSING_ERROR: 'VIDEO_PROCESSING_ERROR',
    AUDIO_PROCESSING_ERROR: 'AUDIO_PROCESSING_ERROR',
    // AI Provider errors
    AI_PROVIDER_ERROR: 'AI_PROVIDER_ERROR',
    MODEL_NOT_AVAILABLE: 'MODEL_NOT_AVAILABLE',
    // TTS errors
    TTS_PROVIDER_ERROR: 'TTS_PROVIDER_ERROR',
    VOICE_NOT_AVAILABLE: 'VOICE_NOT_AVAILABLE'
};
// AI Provider constants
exports.AI_PROVIDERS = {
    OPENAI: 'OPENAI',
    ANTHROPIC: 'ANTHROPIC',
    GOOGLE: 'GOOGLE',
    MISTRAL: 'MISTRAL',
    COHERE: 'COHERE'
};
// TTS Provider constants
exports.TTS_PROVIDERS = {
    OPENAI: 'OPENAI',
    ELEVENLABS: 'ELEVENLABS',
    GOOGLE: 'GOOGLE',
    AZURE: 'AZURE',
    AWS: 'AWS'
};
// Language constants
exports.LANGUAGES = {
    ENGLISH: 'ENGLISH',
    FRENCH: 'FRENCH',
    SPANISH: 'SPANISH',
    GERMAN: 'GERMAN',
    ITALIAN: 'ITALIAN',
    PORTUGUESE: 'PORTUGUESE',
    DUTCH: 'DUTCH',
    RUSSIAN: 'RUSSIAN',
    CHINESE: 'CHINESE',
    JAPANESE: 'JAPANESE',
    KOREAN: 'KOREAN',
    ARABIC: 'ARABIC'
};
// Video project status constants
exports.VIDEO_PROJECT_STATUS = {
    DRAFT: 'DRAFT',
    PROCESSING: 'PROCESSING',
    COMPLETED: 'COMPLETED',
    FAILED: 'FAILED',
    CANCELLED: 'CANCELLED'
};
// Voice gender constants
exports.VOICE_GENDER = {
    MALE: 'MALE',
    FEMALE: 'FEMALE',
    NEUTRAL: 'NEUTRAL'
};
// User roles
exports.USER_ROLES = {
    USER: 'USER',
    ADMIN: 'ADMIN',
    SUPER_ADMIN: 'SUPER_ADMIN'
};
// API limits
exports.API_LIMITS = {
    MAX_FILE_SIZE: 100 * 1024 * 1024, // 100MB
    MAX_VIDEO_DURATION: 600, // 10 minutes
    MAX_REQUESTS_PER_MINUTE: 60,
    MAX_REQUESTS_PER_HOUR: 1000,
    MAX_REQUESTS_PER_DAY: 10000
};
// File types
exports.ALLOWED_FILE_TYPES = {
    IMAGES: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
    VIDEOS: ['video/mp4', 'video/webm', 'video/quicktime'],
    AUDIO: ['audio/mpeg', 'audio/wav', 'audio/ogg', 'audio/mp3']
};
//# sourceMappingURL=constants.js.map